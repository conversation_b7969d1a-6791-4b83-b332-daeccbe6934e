# Loom Project Makefile
# Supports dev, test, staging, and production environments
# Uses Docker directly instead of docker-compose

.PHONY: dev-start dev-stop test test-backend test-frontend lint lint-backend lint-frontend build build-backend build-frontend clean install install-backend install-frontend network-create network-remove

# Docker network and container names
NETWORK_NAME = loom-network
POSTGRES_CONTAINER = loom-postgres
BACKEND_CONTAINER = loom-backend
FRONTEND_CONTAINER = loom-frontend

# Create Docker network
network-create:
	@docker network inspect $(NETWORK_NAME) >/dev/null 2>&1 || docker network create $(NETWORK_NAME)

# Remove Docker network
network-remove:
	@docker network rm $(NETWORK_NAME) 2>/dev/null || true

# Development commands
dev-start: network-create build-backend build-frontend
	@if [ ! -f backend/.env ]; then \
		cp backend/.env.example backend/.env; \
	fi
	@echo "🚀 Starting PostgreSQL database..."
	@docker run -d --name $(POSTGRES_CONTAINER) \
		--network $(NETWORK_NAME) \
		-e POSTGRES_DB=loom \
		-e POSTGRES_USER=loom_user \
		-e POSTGRES_PASSWORD=loom_password \
		-p 5432:5432 \
		postgres:15-alpine || true
	@echo "🚀 Starting backend service..."
	@docker run -d --name $(BACKEND_CONTAINER) \
		--network $(NETWORK_NAME) \
		-p 8000:8000 \
		-v $(PWD)/backend:/app \
		-v $(PWD)/backend/uploads:/app/uploads \
		--env-file backend/.env \
		loom-backend || true
	@echo "🚀 Starting frontend service..."
	@docker run -d --name $(FRONTEND_CONTAINER) \
		--network $(NETWORK_NAME) \
		-p 3000:80 \
		loom-frontend || true
	@echo "✅ All services started!"
	@echo "🌐 Frontend: http://localhost:3000"
	@echo "🔧 Backend API: http://localhost:8000"

dev-stop:
	@echo "🛑 Stopping all services..."
	@docker stop $(FRONTEND_CONTAINER) $(BACKEND_CONTAINER) $(POSTGRES_CONTAINER) 2>/dev/null || true
	@docker rm $(FRONTEND_CONTAINER) $(BACKEND_CONTAINER) $(POSTGRES_CONTAINER) 2>/dev/null || true

dev-logs:
	@echo "📋 Backend logs:"
	@docker logs -f $(BACKEND_CONTAINER)

dev-logs-frontend:
	@echo "📋 Frontend logs:"
	@docker logs -f $(FRONTEND_CONTAINER)

dev-logs-postgres:
	@echo "📋 PostgreSQL logs:"
	@docker logs -f $(POSTGRES_CONTAINER)

dev-restart: dev-stop dev-start

# Testing commands
test: test-backend test-frontend

test-backend:
	@docker exec $(BACKEND_CONTAINER) python -m pytest tests/ -v --cov=server --cov-report=html --cov-report=term

test-frontend:
	@docker run --rm -v $(PWD)/frontend:/app -w /app node:18-alpine npm test -- --run

test-frontend-watch:
	@docker run --rm -it -v $(PWD)/frontend:/app -w /app node:18-alpine npm test

test-frontend-coverage:
	@docker run --rm -v $(PWD)/frontend:/app -w /app node:18-alpine npm run test:coverage

# Linting commands
lint: lint-backend lint-frontend

lint-backend:
	@echo "🔍 Running backend linting..."
	@docker exec -u root $(BACKEND_CONTAINER) python -m black server/ tests/ scripts/ --check
	@docker exec -u root $(BACKEND_CONTAINER) python -m flake8 server/ tests/ scripts/
	@docker exec -u root $(BACKEND_CONTAINER) python -m mypy server/

lint-frontend:
	@docker run --rm -v $(PWD)/frontend:/app -w /app node:18-alpine npm run lint

# Build commands
build: build-backend build-frontend

build-backend:
	@echo "🔨 Building backend image..."
	@docker build -t loom-backend ./backend

build-frontend:
	@echo "🔨 Building frontend image..."
	@docker build -t loom-frontend ./frontend

# Installation commands
install: install-backend install-frontend

install-backend: build-backend

install-frontend: build-frontend

# Utility commands
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/.coverage 2>/dev/null || true
	rm -rf backend/htmlcov 2>/dev/null || true
	@if [ -d "frontend/dist" ]; then rm -rf frontend/dist; fi
	@if [ -d "frontend/build" ]; then rm -rf frontend/build; fi
	@echo "🧹 Cleaning up Docker containers and images..."
	@docker stop $(FRONTEND_CONTAINER) $(BACKEND_CONTAINER) $(POSTGRES_CONTAINER) 2>/dev/null || true
	@docker rm $(FRONTEND_CONTAINER) $(BACKEND_CONTAINER) $(POSTGRES_CONTAINER) 2>/dev/null || true
	@docker rmi loom-backend loom-frontend 2>/dev/null || true

# Format code (auto-fix)
format-backend:
	@echo "🎨 Formatting backend code..."
	@docker exec -u root $(BACKEND_CONTAINER) python -m black server/ tests/ scripts/

# Fix linting issues
lint-fix-backend:
	@echo "🔧 Auto-fixing backend linting issues..."
	@docker exec -u root $(BACKEND_CONTAINER) python -m black server/ tests/ scripts/
	@echo "✅ Code formatted with black"

# Database commands
db-migrate:
	@docker exec $(BACKEND_CONTAINER) alembic upgrade head

db-reset:
	@docker exec $(BACKEND_CONTAINER) alembic downgrade base
	@docker exec $(BACKEND_CONTAINER) alembic upgrade head

db-revision:
	@docker exec $(BACKEND_CONTAINER) alembic revision --autogenerate -m "$(MESSAGE)"

db-current:
	@docker exec $(BACKEND_CONTAINER) alembic current

db-history:
	@docker exec $(BACKEND_CONTAINER) alembic history

db-show:
	@docker exec $(POSTGRES_CONTAINER) psql -U loom_user -d loom -c "\dt"

# Development data seeding
seed-data:
	@docker exec $(BACKEND_CONTAINER) python scripts/seed_professional_datasets.py

seed-data-reset:
	@docker exec $(BACKEND_CONTAINER) python scripts/seed_professional_datasets.py --reset

# Large-scale performance data seeding
seed-large-data:
	@docker exec $(BACKEND_CONTAINER) python scripts/seed_large_dataset.py

seed-large-data-reset:
	@docker exec $(BACKEND_CONTAINER) python scripts/seed_large_dataset.py --reset

seed-large-data-custom:
	@echo "Usage: make seed-large-data-custom POINTS=500000"
	@if [ -z "$(POINTS)" ]; then \
		echo "Error: POINTS parameter is required"; \
		echo "Example: make seed-large-data-custom POINTS=500000"; \
		exit 1; \
	fi
	@docker exec $(BACKEND_CONTAINER) python scripts/seed_large_dataset.py --points $(POINTS)

# Docker-specific commands
docker-build: build

docker-clean: clean network-remove
	@docker system prune -f

docker-shell-backend:
	@docker exec -it $(BACKEND_CONTAINER) /bin/bash

docker-shell-frontend:
	@docker exec -it $(FRONTEND_CONTAINER) /bin/sh

docker-shell-db:
	@docker exec -it $(POSTGRES_CONTAINER) psql -U loom_user -d loom

# Status and monitoring
status:
	@echo "📊 Container Status:"
	@docker ps --filter "name=loom-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

logs-all:
	@echo "📋 All service logs:"
	@docker logs $(POSTGRES_CONTAINER) --tail=50 2>/dev/null || echo "PostgreSQL not running"
	@docker logs $(BACKEND_CONTAINER) --tail=50 2>/dev/null || echo "Backend not running"
	@docker logs $(FRONTEND_CONTAINER) --tail=50 2>/dev/null || echo "Frontend not running"