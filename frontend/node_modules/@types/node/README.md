# Installation
> `npm install --save @types/node`

# Summary
This package contains type definitions for node (https://nodejs.org/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node/v16.

### Additional Details
 * Last updated: Tue, 04 Feb 2025 00:04:06 GMT
 * Dependencies: none

# Credits
These definitions were written by [Microsoft TypeScript](https://github.com/Microsoft), [<PERSON>](https://github.com/jkomyno), [<PERSON><PERSON> HT Tang](https://github.com/alvis), [<PERSON>](https://github.com/r3nya), [<PERSON>](https://github.com/btoueg), [Chigozirim C.](https://github.com/smac89), [<PERSON>](https://github.com/touffy), [Deividas Bakanas](https://github.com/DeividasBakanas), [<PERSON>](https://github.com/eyqs), [<PERSON><PERSON>](https://github.com/<PERSON><PERSON>-<PERSON>-<PERSON>), [<PERSON><PERSON>](https://github.com/hoo29), [<PERSON><PERSON>](https://github.com/kjin), [<PERSON> <PERSON>nh<PERSON>](https://github.com/ajafff), [Lishude](https://github.com/islishude), [<PERSON>z Wiktorczyk](https://github.com/mwiktorczyk), [Mohsen Azimi](https://github.com/mohsen1), [Nikita Galkin](https://github.com/galkin), [Parambir Singh](https://github.com/parambirs), [Sebastian Silbermann](https://github.com/eps1lon), [Seth Westphal](https://github.com/westy92), [Simon Schick](https://github.com/SimonSchick), [Thomas den Hollander](https://github.com/ThomasdenH), [Wilco Bakker](https://github.com/WilcoBakker), [wwwy3y3](https://github.com/wwwy3y3), [Samuel Ainsworth](https://github.com/samuela), [Kyle Uehlein](https://github.com/kuehlein), [Thanik Bhongbhibhat](https://github.com/bhongy), [Marcin Kopacz](https://github.com/chyzwar), [Trivikram Kamat](https://github.com/trivikr), [Junxiao Shi](https://github.com/yoursunny), [Ilia Baryshnikov](https://github.com/qwelias), [ExE Boss](https://github.com/ExE-Boss), [Piotr Błażejewicz](https://github.com/peterblazejewicz), [Anna Henningsen](https://github.com/addaleax), [Victor Perin](https://github.com/victorperin), [NodeJS Contributors](https://github.com/NodeJS), [Linus Unnebäck](https://github.com/LinusU), and [wafuwafu13](https://github.com/wafuwafu13).
