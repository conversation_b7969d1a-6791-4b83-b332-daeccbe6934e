import React, { useState } from 'react';
import './DataTable.css';

// Mock data for demonstration
const mockData = [
  { id: 1, x: 0.123, y: 0.456, z: 0.789, radius: 0.5, imageId: 'img_001', metadata: 'Sample point 1' },
  { id: 2, x: -0.234, y: 0.567, z: -0.123, radius: 0.3, imageId: 'img_002', metadata: 'Sample point 2' },
  { id: 3, x: 0.345, y: -0.678, z: 0.234, radius: 0.7, imageId: 'img_003', metadata: 'Sample point 3' },
  { id: 4, x: -0.456, y: -0.789, z: -0.345, radius: 0.4, imageId: 'img_004', metadata: 'Sample point 4' },
  { id: 5, x: 0.567, y: 0.123, z: 0.456, radius: 0.6, imageId: 'img_005', metadata: 'Sample point 5' },
];

const DataTable: React.FC = () => {
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [sortColumn, setSortColumn] = useState<string>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleRowSelect = (id: number) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedRows(newSelected);
  };

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (column: string) => {
    if (sortColumn !== column) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="data-table">
      <div className="table-header">
        <div className="table-info">
          <span>Showing {mockData.length} points</span>
          {selectedRows.size > 0 && (
            <span className="selection-info">
              ({selectedRows.size} selected)
            </span>
          )}
        </div>
        <div className="table-actions">
          <button className="action-button">Export Selected</button>
          <button className="action-button">Clear Selection</button>
        </div>
      </div>

      <div className="table-container">
        <table className="points-table">
          <thead>
            <tr>
              <th className="checkbox-column">
                <input type="checkbox" />
              </th>
              <th onClick={() => handleSort('id')} className="sortable">
                ID {getSortIcon('id')}
              </th>
              <th onClick={() => handleSort('x')} className="sortable">
                X {getSortIcon('x')}
              </th>
              <th onClick={() => handleSort('y')} className="sortable">
                Y {getSortIcon('y')}
              </th>
              <th onClick={() => handleSort('z')} className="sortable">
                Z {getSortIcon('z')}
              </th>
              <th onClick={() => handleSort('radius')} className="sortable">
                Radius {getSortIcon('radius')}
              </th>
              <th onClick={() => handleSort('imageId')} className="sortable">
                Image ID {getSortIcon('imageId')}
              </th>
              <th>Metadata</th>
            </tr>
          </thead>
          <tbody>
            {mockData.map((point) => (
              <tr 
                key={point.id}
                className={selectedRows.has(point.id) ? 'selected' : ''}
              >
                <td className="checkbox-column">
                  <input 
                    type="checkbox"
                    checked={selectedRows.has(point.id)}
                    onChange={() => handleRowSelect(point.id)}
                  />
                </td>
                <td>{point.id}</td>
                <td>{point.x.toFixed(3)}</td>
                <td>{point.y.toFixed(3)}</td>
                <td>{point.z.toFixed(3)}</td>
                <td>{point.radius.toFixed(1)}</td>
                <td>{point.imageId}</td>
                <td>{point.metadata}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DataTable;
