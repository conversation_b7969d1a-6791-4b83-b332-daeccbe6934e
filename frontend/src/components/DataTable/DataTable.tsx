import React, { useState } from 'react';
import './DataTable.css';
import { EmbeddingPoint3D } from '../../services/api';

interface DataTableProps {
  data: EmbeddingPoint3D[];
  loading: boolean;
}

const DataTable: React.FC<DataTableProps> = ({ data, loading }) => {
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleRowSelect = (id: number) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedRows(newSelected);
  };

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (column: string) => {
    if (sortColumn !== column) return '↕️';
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="data-table">
      <div className="table-header">
        <div className="table-info">
          <span>
            {loading ? 'Loading...' : `Showing ${data.length} points`}
          </span>
          {selectedRows.size > 0 && (
            <span className="selection-info">
              ({selectedRows.size} selected)
            </span>
          )}
        </div>
        <div className="table-actions">
          <button className="action-button">Export Selected</button>
          <button className="action-button">Clear Selection</button>
        </div>
      </div>

      <div className="table-container">
        <table className="points-table">
          <thead>
            <tr>
              <th className="checkbox-column">
                <input type="checkbox" />
              </th>
              <th onClick={() => handleSort('id')} className="sortable">
                ID {getSortIcon('id')}
              </th>
              <th onClick={() => handleSort('x')} className="sortable">
                X {getSortIcon('x')}
              </th>
              <th onClick={() => handleSort('y')} className="sortable">
                Y {getSortIcon('y')}
              </th>
              <th onClick={() => handleSort('z')} className="sortable">
                Z {getSortIcon('z')}
              </th>
              <th onClick={() => handleSort('point_radius')} className="sortable">
                Radius {getSortIcon('point_radius')}
              </th>
              <th onClick={() => handleSort('image_robot_id')} className="sortable">
                Robot ID {getSortIcon('image_robot_id')}
              </th>
              <th onClick={() => handleSort('method')} className="sortable">
                Method {getSortIcon('method')}
              </th>
            </tr>
          </thead>
          <tbody>
            {data.map((point) => (
              <tr
                key={point.id}
                className={selectedRows.has(point.id) ? 'selected' : ''}
              >
                <td className="checkbox-column">
                  <input
                    type="checkbox"
                    checked={selectedRows.has(point.id)}
                    onChange={() => handleRowSelect(point.id)}
                  />
                </td>
                <td>{point.id}</td>
                <td>{point.x.toFixed(3)}</td>
                <td>{point.y.toFixed(3)}</td>
                <td>{point.z?.toFixed(3) || 'N/A'}</td>
                <td>{point.point_radius.toFixed(1)}</td>
                <td>{point.image_robot_id}</td>
                <td>{point.method}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DataTable;
