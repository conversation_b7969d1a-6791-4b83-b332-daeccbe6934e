import React from 'react';
import { render, screen, fireEvent } from '../../../test-utils';
import DataTable from '../DataTable';
import { mockVisualizationData } from '../../../test-utils';

const mockData = mockVisualizationData.points;

describe('DataTable', () => {
  it('should render table headers', () => {
    render(<DataTable data={mockData} loading={false} />);

    expect(screen.getByText(/ID/)).toBeInTheDocument();
    expect(screen.getByText(/X/)).toBeInTheDocument();
    expect(screen.getByText(/Y/)).toBeInTheDocument();
    expect(screen.getByText(/Z/)).toBeInTheDocument();
    expect(screen.getByText(/Radius/)).toBeInTheDocument();
    expect(screen.getByText(/Robot ID/)).toBeInTheDocument();
    expect(screen.getByText(/Method/)).toBeInTheDocument();
  });

  it('should render data rows', () => {
    render(<DataTable data={mockData} loading={false} />);

    expect(screen.getByText('1')).toBeInTheDocument(); // ID
    expect(screen.getByText('0.123')).toBeInTheDocument(); // X
    expect(screen.getByText('0.456')).toBeInTheDocument(); // Y
    expect(screen.getByText('0.789')).toBeInTheDocument(); // Z
    expect(screen.getByText('5.2')).toBeInTheDocument(); // Radius
    expect(screen.getByText('robot_001')).toBeInTheDocument(); // Robot ID
    expect(screen.getByText('pca')).toBeInTheDocument(); // Method
  });

  it('should show point count in header', () => {
    render(<DataTable data={mockData} loading={false} />);

    expect(screen.getByText('Showing 1 points')).toBeInTheDocument();
  });

  it('should show loading state', () => {
    render(<DataTable data={[]} loading={true} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should handle empty data', () => {
    render(<DataTable data={[]} loading={false} />);

    expect(screen.getByText('Showing 0 points')).toBeInTheDocument();
    // Should still render headers
    expect(screen.getByText(/ID/)).toBeInTheDocument();
  });

  it('should handle row selection', () => {
    render(<DataTable data={mockData} loading={false} />);

    const checkbox = screen.getAllByRole('checkbox')[1]; // First data row checkbox
    fireEvent.click(checkbox);

    expect(checkbox).toBeChecked();
    expect(screen.getByText('(1 selected)')).toBeInTheDocument();
  });

  it('should handle multiple row selection', () => {
    const multipleData = [
      ...mockData,
      {
        ...mockData[0],
        id: 2,
        x: 0.234,
        y: 0.567,
        z: 0.890
      }
    ];

    render(<DataTable data={multipleData} loading={false} />);

    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[1]); // First data row
    fireEvent.click(checkboxes[2]); // Second data row

    expect(screen.getByText('(2 selected)')).toBeInTheDocument();
  });

  it('should render action buttons', () => {
    render(<DataTable data={mockData} loading={false} />);

    expect(screen.getByText('Export Selected')).toBeInTheDocument();
    expect(screen.getByText('Clear Selection')).toBeInTheDocument();
  });

  it('should handle sorting by clicking column headers', () => {
    render(<DataTable data={mockData} loading={false} />);

    const idHeader = screen.getByText(/ID/);
    fireEvent.click(idHeader);

    // Should show sort indicator (up arrow for ascending)
    expect(screen.getByText(/ID.*↑/)).toBeInTheDocument();
  });

  it('should toggle sort direction on repeated clicks', () => {
    render(<DataTable data={mockData} loading={false} />);

    const idHeader = screen.getByText(/ID/);

    // First click - ascending
    fireEvent.click(idHeader);
    expect(screen.getByText(/ID.*↑/)).toBeInTheDocument();

    // Second click - descending
    fireEvent.click(idHeader);
    expect(screen.getByText(/ID.*↓/)).toBeInTheDocument();
  });

  it('should handle Z coordinate being null/undefined', () => {
    const dataWithNullZ = [{
      ...mockData[0],
      z: undefined
    }];

    render(<DataTable data={dataWithNullZ} loading={false} />);

    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('should format numeric values correctly', () => {
    const dataWithPreciseValues = [{
      ...mockData[0],
      x: 0.123456789,
      y: 0.987654321,
      point_radius: 5.23456
    }];

    render(<DataTable data={dataWithPreciseValues} loading={false} />);

    expect(screen.getByText('0.123')).toBeInTheDocument(); // X rounded to 3 decimals
    expect(screen.getByText('0.988')).toBeInTheDocument(); // Y rounded to 3 decimals
    expect(screen.getByText('5.2')).toBeInTheDocument(); // Radius rounded to 1 decimal
  });

  it('should show selection info only when rows are selected', () => {
    render(<DataTable data={mockData} loading={false} />);

    // Initially no selection info
    expect(screen.queryByText(/selected/)).not.toBeInTheDocument();

    // Select a row
    const checkbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(checkbox);

    // Now selection info should appear
    expect(screen.getByText('(1 selected)')).toBeInTheDocument();
  });
});
