import React from 'react';
import { render, screen, fireEvent } from '../../../test-utils';
import Visualization3D from '../Visualization3D';
import { mockVisualizationData } from '../../../test-utils';

describe('Visualization3D', () => {
  it('should render placeholder when no data', () => {
    render(<Visualization3D data={null} loading={false} error={null} />);

    expect(screen.getByText('3D Visualization')).toBeInTheDocument();
    expect(screen.getByText('3D scatter plot will be rendered here')).toBeInTheDocument();
  });

  it('should show loading overlay when loading', () => {
    render(<Visualization3D data={null} loading={true} error={null} />);

    expect(screen.getByText('Loading visualization...')).toBeInTheDocument();
    const loadingOverlay = screen.getByText('Loading visualization...').closest('.loading-overlay');
    expect(loadingOverlay).toHaveStyle('display: flex');
  });

  it('should hide loading overlay when not loading', () => {
    render(<Visualization3D data={null} loading={false} error={null} />);

    const loadingOverlay = document.querySelector('.loading-overlay');
    expect(loadingOverlay).toHaveStyle('display: none');
  });

  it('should display error state', () => {
    const errorMessage = 'Failed to load data';
    render(<Visualization3D data={null} loading={false} error={errorMessage} />);

    expect(screen.getByText('Error Loading Data')).toBeInTheDocument();
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should handle retry button click', () => {
    // Mock window.location.reload
    const mockReload = jest.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: mockReload },
      writable: true
    });

    render(<Visualization3D data={null} loading={false} error="Test error" />);

    const retryButton = screen.getByText('Retry');
    fireEvent.click(retryButton);

    expect(mockReload).toHaveBeenCalled();
  });

  it('should display data statistics when data is available', () => {
    render(<Visualization3D data={mockVisualizationData} loading={false} error={null} />);

    expect(screen.getByText('pca visualization')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // total_points
    expect(screen.getByText('pca')).toBeInTheDocument(); // method
    expect(screen.getByText('resnet50')).toBeInTheDocument(); // model_id
  });

  it('should show default values when data fields are missing', () => {
    const incompleteData = {
      ...mockVisualizationData,
      method: undefined,
      model_id: undefined,
      total_points: 0
    };

    render(<Visualization3D data={incompleteData} loading={false} error={null} />);

    expect(screen.getByText('0')).toBeInTheDocument(); // total_points
    expect(screen.getAllByText('None')).toHaveLength(2); // method and model_id
  });

  it('should render statistics labels correctly', () => {
    render(<Visualization3D data={mockVisualizationData} loading={false} error={null} />);

    expect(screen.getByText('Points:')).toBeInTheDocument();
    expect(screen.getByText('Method:')).toBeInTheDocument();
    expect(screen.getByText('Model:')).toBeInTheDocument();
  });

  it('should have correct CSS classes', () => {
    render(<Visualization3D data={null} loading={false} error={null} />);

    const container = document.querySelector('.visualization-3d');
    expect(container).toBeInTheDocument();

    const placeholder = document.querySelector('.visualization-placeholder');
    expect(placeholder).toBeInTheDocument();

    const stats = document.querySelector('.placeholder-stats');
    expect(stats).toBeInTheDocument();
  });

  it('should show loading spinner in overlay', () => {
    render(<Visualization3D data={null} loading={true} error={null} />);

    const spinner = document.querySelector('.loading-spinner');
    expect(spinner).toBeInTheDocument();
  });

  it('should handle null/undefined data gracefully', () => {
    render(<Visualization3D data={null} loading={false} error={null} />);

    // Should not crash and should show default placeholder
    expect(screen.getByText('3D scatter plot will be rendered here')).toBeInTheDocument();
    expect(screen.getByText('0')).toBeInTheDocument(); // Points count
    expect(screen.getAllByText('None')).toHaveLength(2); // Method and Model
  });

  it('should prioritize error state over loading state', () => {
    render(<Visualization3D data={null} loading={true} error="Test error" />);

    // Should show error, not loading
    expect(screen.getByText('Error Loading Data')).toBeInTheDocument();
    expect(screen.queryByText('Loading visualization...')).not.toBeInTheDocument();
  });
});
