import React from 'react';
import './FilterPanel.css';

const FilterPanel: React.FC = () => {
  return (
    <div className="filter-panel">
      <div className="filter-section">
        <h4>Visualization Controls</h4>
        <div className="filter-group">
          <label htmlFor="point-size">Point Size:</label>
          <input 
            type="range" 
            id="point-size" 
            min="0.1" 
            max="2.0" 
            step="0.1" 
            defaultValue="0.75"
          />
          <span>0.75</span>
        </div>
        
        <div className="filter-group">
          <label htmlFor="camera-controls">Camera:</label>
          <div className="button-group">
            <button className="control-button">Reset View</button>
            <button className="control-button">Top View</button>
            <button className="control-button">Side View</button>
          </div>
        </div>
      </div>

      <div className="filter-section">
        <h4>Data Filters</h4>
        <div className="filter-group">
          <label htmlFor="search">Search:</label>
          <input 
            type="text" 
            id="search" 
            placeholder="Search data points..."
            className="search-input"
          />
        </div>
        
        <div className="filter-group">
          <label htmlFor="limit">Display Limit:</label>
          <select id="limit" className="select-input">
            <option value="1000">1,000 points</option>
            <option value="5000">5,000 points</option>
            <option value="10000">10,000 points</option>
            <option value="50000" selected>50,000 points</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;
