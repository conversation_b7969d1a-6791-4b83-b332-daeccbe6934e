.filter-panel {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
}

.filter-section {
  flex: 1;
  min-width: 300px;
}

.filter-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.filter-group label {
  font-weight: 500;
  color: #495057;
  min-width: 100px;
  font-size: 14px;
}

.filter-group input[type="range"] {
  flex: 1;
  max-width: 150px;
}

.filter-group span {
  font-size: 14px;
  color: #6c757d;
  min-width: 40px;
}

.search-input,
.select-input {
  flex: 1;
  max-width: 200px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus,
.select-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.control-button {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.control-button:hover {
  background-color: #5a6268;
}

.control-button:active {
  background-color: #495057;
}
