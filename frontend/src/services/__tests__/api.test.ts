import { api } from '../api';
import { server } from '../../mocks/server';
import { rest } from 'msw';

describe('API Service', () => {
  describe('getVisualizationData', () => {
    it('should fetch visualization data successfully', async () => {
      const data = await api.getVisualizationData();
      
      expect(data).toHaveProperty('method');
      expect(data).toHaveProperty('total_points');
      expect(data).toHaveProperty('points');
      expect(data).toHaveProperty('bounds');
      expect(Array.isArray(data.points)).toBe(true);
    });

    it('should apply filters correctly', async () => {
      const filters = {
        method: 'pca',
        model_id: 'resnet50',
        limit: 2
      };
      
      const data = await api.getVisualizationData(filters);
      
      expect(data.method).toBe('pca');
      expect(data.model_id).toBe('resnet50');
      expect(data.points.length).toBeLessThanOrEqual(2);
    });

    it('should handle API errors', async () => {
      // Override the handler to return an error
      server.use(
        rest.get('http://localhost:8000/api/v1/images/visualization/3d', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ error: 'Server error' }));
        })
      );

      await expect(api.getVisualizationData()).rejects.toThrow('Failed to fetch visualization data');
    });
  });

  describe('getImagePoints', () => {
    it('should fetch image points successfully', async () => {
      const points = await api.getImagePoints(1001);
      
      expect(Array.isArray(points)).toBe(true);
      expect(points.length).toBeGreaterThan(0);
      expect(points[0]).toHaveProperty('id');
      expect(points[0]).toHaveProperty('x');
      expect(points[0]).toHaveProperty('y');
      expect(points[0]).toHaveProperty('radius');
    });

    it('should handle API errors for image points', async () => {
      server.use(
        rest.get('http://localhost:8000/api/v1/images/:imageId/points', (req, res, ctx) => {
          return res(ctx.status(404), ctx.json({ error: 'Image not found' }));
        })
      );

      await expect(api.getImagePoints(9999)).rejects.toThrow('Failed to fetch points');
    });
  });

  describe('Filter options', () => {
    it('should fetch available robots', async () => {
      const robots = await api.getAvailableRobots();
      
      expect(Array.isArray(robots)).toBe(true);
      expect(robots.length).toBeGreaterThan(0);
      expect(robots).toContain('robot_001');
    });

    it('should fetch available models', async () => {
      const models = await api.getAvailableModels();
      
      expect(Array.isArray(models)).toBe(true);
      expect(models.length).toBeGreaterThan(0);
      expect(models).toContain('resnet50');
    });

    it('should fetch available methods', async () => {
      const methods = await api.getAvailableMethods();
      
      expect(Array.isArray(methods)).toBe(true);
      expect(methods.length).toBeGreaterThan(0);
      expect(methods).toContain('pca');
    });
  });

  describe('healthCheck', () => {
    it('should return health status', async () => {
      const health = await api.healthCheck();
      
      expect(health).toHaveProperty('status');
      expect(health.status).toBe('ok');
    });
  });
});
