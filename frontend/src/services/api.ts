// API service for backend communication

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Types based on backend schemas
export interface EmbeddingPoint3D {
  id: number;
  x: number;
  y: number;
  z?: number;
  method: string;
  model_id: string;
  point_id: number;
  point_x: number;
  point_y: number;
  point_radius: number;
  image_id: number;
  image_captured_at: number;
  image_latitude?: number;
  image_longitude?: number;
  image_robot_id: string;
}

export interface VisualizationResponse {
  method: string;
  model_id?: string;
  total_points: number;
  points: EmbeddingPoint3D[];
  bounds: {
    x: [number, number];
    y: [number, number];
    z: [number, number];
  };
}

export interface PointResponse {
  id: number;
  image_id: number;
  x: number;
  y: number;
  radius: number;
  created_at: number;
  updated_at: number;
  image_captured_at?: number;
  image_latitude?: number;
  image_longitude?: number;
  image_height?: number;
  image_width?: number;
  image_robot_id?: string;
}

export interface VisualizationFilters {
  method?: string;
  model_id?: string;
  robot_ids?: string[];
  limit?: number;
  x_min?: number;
  x_max?: number;
  y_min?: number;
  y_max?: number;
  z_min?: number;
  z_max?: number;
}

// API functions
export const api = {
  // Get 3D visualization data
  async getVisualizationData(filters: VisualizationFilters = {}): Promise<VisualizationResponse> {
    const params = new URLSearchParams();

    // Method is required by the backend
    const method = filters.method || 'umap_3d';
    params.append('method', method);

    if (filters.model_id) params.append('model_id', filters.model_id);
    if (filters.limit) params.append('sample_size', filters.limit.toString());
    if (filters.x_min !== undefined) params.append('x_min', filters.x_min.toString());
    if (filters.x_max !== undefined) params.append('x_max', filters.x_max.toString());
    if (filters.y_min !== undefined) params.append('y_min', filters.y_min.toString());
    if (filters.y_max !== undefined) params.append('y_max', filters.y_max.toString());
    if (filters.z_min !== undefined) params.append('z_min', filters.z_min.toString());
    if (filters.z_max !== undefined) params.append('z_max', filters.z_max.toString());
    if (filters.robot_ids && filters.robot_ids.length > 0) {
      params.append('robot_id', filters.robot_ids[0]); // Backend expects single robot_id
    }

    const response = await fetch(`${API_BASE_URL}/api/v1/images/visualization/3d?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch visualization data: ${response.statusText}`);
    }
    return response.json();
  },

  // Get points for a specific image
  async getImagePoints(
    imageId: number, 
    options: {
      skip?: number;
      limit?: number;
      x_min?: number;
      x_max?: number;
      y_min?: number;
      y_max?: number;
      radius_min?: number;
      radius_max?: number;
    } = {}
  ): Promise<PointResponse[]> {
    const params = new URLSearchParams();
    
    if (options.skip) params.append('skip', options.skip.toString());
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.x_min !== undefined) params.append('x_min', options.x_min.toString());
    if (options.x_max !== undefined) params.append('x_max', options.x_max.toString());
    if (options.y_min !== undefined) params.append('y_min', options.y_min.toString());
    if (options.y_max !== undefined) params.append('y_max', options.y_max.toString());
    if (options.radius_min !== undefined) params.append('radius_min', options.radius_min.toString());
    if (options.radius_max !== undefined) params.append('radius_max', options.radius_max.toString());

    const response = await fetch(`${API_BASE_URL}/api/v1/images/${imageId}/points?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch points: ${response.statusText}`);
    }
    return response.json();
  },

  // Get available filter options
  async getAvailableRobots(): Promise<string[]> {
    const response = await fetch(`${API_BASE_URL}/api/v1/images/filters/robots`);
    if (!response.ok) {
      throw new Error(`Failed to fetch robots: ${response.statusText}`);
    }
    return response.json();
  },

  async getAvailableModels(): Promise<string[]> {
    const response = await fetch(`${API_BASE_URL}/api/v1/images/filters/models`);
    if (!response.ok) {
      throw new Error(`Failed to fetch models: ${response.statusText}`);
    }
    return response.json();
  },

  async getAvailableMethods(): Promise<string[]> {
    const response = await fetch(`${API_BASE_URL}/api/v1/images/filters/methods`);
    if (!response.ok) {
      throw new Error(`Failed to fetch methods: ${response.statusText}`);
    }
    return response.json();
  },

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    const response = await fetch(`${API_BASE_URL}/health`);
    if (!response.ok) {
      throw new Error(`Health check failed: ${response.statusText}`);
    }
    return response.json();
  }
};

export default api;
