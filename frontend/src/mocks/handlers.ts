import { rest } from 'msw';
import { VisualizationResponse, EmbeddingPoint3D, PointResponse } from '../services/api';

const API_BASE_URL = 'http://localhost:8000';

// Mock data
const mockEmbeddingPoints: EmbeddingPoint3D[] = [
  {
    id: 1,
    x: 0.123,
    y: 0.456,
    z: 0.789,
    method: 'pca',
    model_id: 'resnet50',
    point_id: 101,
    point_x: 100.5,
    point_y: 200.3,
    point_radius: 5.2,
    image_id: 1001,
    image_captured_at: 1640995200000,
    image_latitude: 37.7749,
    image_longitude: -122.4194,
    image_robot_id: 'robot_001'
  },
  {
    id: 2,
    x: -0.234,
    y: 0.567,
    z: -0.123,
    method: 'pca',
    model_id: 'resnet50',
    point_id: 102,
    point_x: 150.8,
    point_y: 180.7,
    point_radius: 3.8,
    image_id: 1002,
    image_captured_at: 1640995260000,
    image_latitude: 37.7849,
    image_longitude: -122.4094,
    image_robot_id: 'robot_002'
  },
  {
    id: 3,
    x: 0.345,
    y: -0.678,
    z: 0.234,
    method: 'tsne',
    model_id: 'vit_base',
    point_id: 103,
    point_x: 75.2,
    point_y: 220.1,
    point_radius: 7.1,
    image_id: 1003,
    image_captured_at: 1640995320000,
    image_latitude: 37.7649,
    image_longitude: -122.4294,
    image_robot_id: 'robot_001'
  }
];

const mockVisualizationResponse: VisualizationResponse = {
  method: 'pca',
  model_id: 'resnet50',
  total_points: mockEmbeddingPoints.length,
  points: mockEmbeddingPoints,
  bounds: {
    x: [-0.5, 0.5],
    y: [-0.8, 0.8],
    z: [-0.3, 0.9]
  }
};

const mockPointsResponse: PointResponse[] = [
  {
    id: 101,
    image_id: 1001,
    x: 100.5,
    y: 200.3,
    radius: 5.2,
    created_at: 1640995200000,
    updated_at: 1640995200000,
    image_captured_at: 1640995200000,
    image_latitude: 37.7749,
    image_longitude: -122.4194,
    image_height: 1080,
    image_width: 1920,
    image_robot_id: 'robot_001'
  }
];

export const handlers = [
  // Visualization data endpoint
  rest.get(`${API_BASE_URL}/api/v1/images/visualization/3d`, (req, res, ctx) => {
    const method = req.url.searchParams.get('method');
    const model_id = req.url.searchParams.get('model_id');
    const limit = req.url.searchParams.get('limit');

    // Filter mock data based on query parameters
    let filteredPoints = [...mockEmbeddingPoints];
    
    if (method && method !== '') {
      filteredPoints = filteredPoints.filter(point => point.method === method);
    }
    
    if (model_id && model_id !== '') {
      filteredPoints = filteredPoints.filter(point => point.model_id === model_id);
    }
    
    if (limit) {
      filteredPoints = filteredPoints.slice(0, parseInt(limit));
    }

    const response: VisualizationResponse = {
      ...mockVisualizationResponse,
      method: method || 'pca',
      model_id: model_id || 'resnet50',
      total_points: filteredPoints.length,
      points: filteredPoints
    };

    return res(ctx.json(response));
  }),

  // Image points endpoint
  rest.get(`${API_BASE_URL}/api/v1/images/:imageId/points`, (req, res, ctx) => {
    return res(ctx.json(mockPointsResponse));
  }),

  // Filter options endpoints
  rest.get(`${API_BASE_URL}/api/v1/images/filters/robots`, (req, res, ctx) => {
    return res(ctx.json(['robot_001', 'robot_002', 'robot_003']));
  }),

  rest.get(`${API_BASE_URL}/api/v1/images/filters/models`, (req, res, ctx) => {
    return res(ctx.json(['resnet50', 'vit_base', 'efficientnet_b0']));
  }),

  rest.get(`${API_BASE_URL}/api/v1/images/filters/methods`, (req, res, ctx) => {
    return res(ctx.json(['pca', 'tsne', 'umap']));
  }),

  // Health check endpoint
  rest.get(`${API_BASE_URL}/health`, (req, res, ctx) => {
    return res(ctx.json({ status: 'ok' }));
  }),

  // Error simulation handlers for testing error states
  rest.get(`${API_BASE_URL}/api/v1/images/visualization-error`, (req, res, ctx) => {
    return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
  })
];
