import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../test-utils';
import MainPage from '../MainPage';

describe('MainPage Integration', () => {
  it('should render all main sections', async () => {
    render(<MainPage />);

    // Check for main layout sections
    expect(screen.getByText('Filters & Controls')).toBeInTheDocument();
    expect(screen.getByText('Data Points')).toBeInTheDocument();
    
    // Wait for data to load and check visualization
    await waitFor(() => {
      expect(screen.getByText('3D Visualization')).toBeInTheDocument();
    });
  });

  it('should load and display data from API', async () => {
    render(<MainPage />);

    // Wait for API calls to complete
    await waitFor(() => {
      expect(screen.getAllByText('pca')).toHaveLength(4); // Method appears in multiple places
    });

    // Check that filter options are loaded
    expect(screen.getByText('All Methods')).toBeInTheDocument();
    expect(screen.getByText('All Models')).toBeInTheDocument();

    // Check that data table shows data
    await waitFor(() => {
      expect(screen.getByText('Showing 3 points')).toBeInTheDocument();
    });
  });

  it('should handle filter changes and update data', async () => {
    render(<MainPage />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('Showing 3 points')).toBeInTheDocument();
    });

    // Change method filter
    const methodSelect = screen.getByLabelText('Method:');
    fireEvent.change(methodSelect, { target: { value: 'tsne' } });

    // Should trigger new API call and update visualization
    await waitFor(() => {
      expect(screen.getByText('tsne visualization')).toBeInTheDocument();
    });
  });

  it('should handle panel collapse/expand', () => {
    render(<MainPage />);

    // Find collapse buttons
    const filterCollapseButton = screen.getAllByText('▲')[0];
    const tableCollapseButton = screen.getAllByText('▼')[0];

    // Collapse filter panel
    fireEvent.click(filterCollapseButton);
    expect(screen.getAllByText('▼')).toHaveLength(2); // Both buttons should be ▼

    // Collapse table panel
    fireEvent.click(tableCollapseButton);
    expect(screen.getAllByText('▲')).toHaveLength(1); // One button should be ▲
  });

  it('should handle resizable panels', () => {
    render(<MainPage />);

    // Check that resize handles are present (they have specific CSS classes)
    const resizeHandles = document.querySelectorAll('.resize-handle');
    expect(resizeHandles.length).toBeGreaterThan(0);
  });

  it('should show loading states initially', () => {
    render(<MainPage />);

    // Should show loading in visualization
    expect(screen.getByText('Loading visualization...')).toBeInTheDocument();
  });

  it('should handle API errors gracefully', async () => {
    // This test would require mocking error responses
    // For now, we'll just ensure the error handling structure is in place
    render(<MainPage />);

    // The error handling is built into the hooks and components
    // If there were an error, it would be displayed in the Visualization3D component
    await waitFor(() => {
      // Just ensure the page renders without crashing
      expect(screen.getByText('3D Visualization')).toBeInTheDocument();
    });
  });

  it('should pass data between components correctly', async () => {
    render(<MainPage />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Showing 3 points')).toBeInTheDocument();
    });

    // Check that the same data appears in both visualization and table
    expect(screen.getAllByText('pca')).toHaveLength(4); // In multiple places
    expect(screen.getAllByText('robot_001')).toHaveLength(2); // In table (appears twice)
  });

  it('should maintain filter state correctly', async () => {
    render(<MainPage />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('All Methods')).toBeInTheDocument();
    });

    // Change a filter
    const limitSelect = screen.getByLabelText('Display Limit:');
    fireEvent.change(limitSelect, { target: { value: '1000' } });

    // The filter should maintain its state
    expect(limitSelect).toHaveValue('1000');
  });

  it('should handle point size control', async () => {
    render(<MainPage />);

    // Wait for components to load
    await waitFor(() => {
      expect(screen.getByLabelText('Point Size:')).toBeInTheDocument();
    });

    const pointSizeSlider = screen.getByLabelText('Point Size:');
    expect(pointSizeSlider).toHaveValue('0.75'); // Default value

    // Change point size
    fireEvent.change(pointSizeSlider, { target: { value: '1.0' } });
    expect(screen.getByText('1.0')).toBeInTheDocument();
  });
});
