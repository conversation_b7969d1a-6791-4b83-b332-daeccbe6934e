import React, { useState } from 'react';
import './MainPage.css';
import FilterPanel from '../components/FilterPanel/FilterPanel';
import Visualization3D from '../components/Visualization3D/Visualization3D';
import DataTable from '../components/DataTable/DataTable';

const MainPage: React.FC = () => {
  const [isFilterPanelCollapsed, setIsFilterPanelCollapsed] = useState(false);
  const [isDataTableCollapsed, setIsDataTableCollapsed] = useState(false);

  return (
    <div className="main-page">
      {/* Top Filter Panel */}
      <div className={`filter-panel-container ${isFilterPanelCollapsed ? 'collapsed' : ''}`}>
        <div className="panel-header">
          <h3>Filters & Controls</h3>
          <button 
            className="collapse-button"
            onClick={() => setIsFilterPanelCollapsed(!isFilterPanelCollapsed)}
          >
            {isFilterPanelCollapsed ? '▼' : '▲'}
          </button>
        </div>
        {!isFilterPanelCollapsed && (
          <div className="panel-content">
            <FilterPanel />
          </div>
        )}
      </div>

      {/* Main 3D Visualization */}
      <div className="visualization-container">
        <Visualization3D />
      </div>

      {/* Bottom Data Table */}
      <div className={`data-table-container ${isDataTableCollapsed ? 'collapsed' : ''}`}>
        <div className="panel-header">
          <h3>Data Points</h3>
          <button 
            className="collapse-button"
            onClick={() => setIsDataTableCollapsed(!isDataTableCollapsed)}
          >
            {isDataTableCollapsed ? '▲' : '▼'}
          </button>
        </div>
        {!isDataTableCollapsed && (
          <div className="panel-content">
            <DataTable />
          </div>
        )}
      </div>
    </div>
  );
};

export default MainPage;
