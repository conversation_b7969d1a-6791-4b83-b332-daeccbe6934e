.main-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Filter Panel (Top) */
.filter-panel-container {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  flex-shrink: 0;
  position: relative;
  min-height: 50px;
  max-height: 400px;
}

.filter-panel-container.collapsed {
  height: 50px !important;
}

/* Main Visualization (Center) */
.visualization-container {
  flex: 1;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
  min-height: 200px;
}

/* Data Table (Bottom) */
.data-table-container {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  flex-shrink: 0;
  position: relative;
  min-height: 50px;
  max-height: 500px;
}

.data-table-container.collapsed {
  height: 50px !important;
}

/* Panel Headers */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  min-height: 50px;
  box-sizing: border-box;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.collapse-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #6c757d;
  transition: background-color 0.2s ease;
}

.collapse-button:hover {
  background-color: #dee2e6;
  color: #495057;
}

/* Panel Content */
.panel-content {
  padding: 20px;
  overflow-y: auto;
}

.filter-panel-container .panel-content {
  height: calc(100% - 50px);
  overflow-y: auto;
}

.data-table-container .panel-content {
  height: calc(100% - 50px);
  overflow: hidden;
}

/* Resize Handles */
.resize-handle {
  position: absolute;
  left: 0;
  right: 0;
  height: 6px;
  background-color: transparent;
  cursor: ns-resize;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: #007bff;
}

.resize-handle:active {
  background-color: #0056b3;
}

.resize-handle-bottom {
  bottom: 0;
}

.resize-handle-top {
  top: 0;
}

/* Visual indicator for resize handles */
.resize-handle::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 2px;
  background-color: #6c757d;
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resize-handle:hover::before {
  opacity: 0.6;
}

.resize-handle:active::before {
  opacity: 1;
  background-color: #007bff;
}

/* Prevent text selection during resize */
.main-page.resizing {
  user-select: none;
}

.main-page.resizing * {
  pointer-events: none;
}

.main-page.resizing .resize-handle {
  pointer-events: auto;
}
