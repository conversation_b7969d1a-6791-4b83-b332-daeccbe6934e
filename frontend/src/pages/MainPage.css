.main-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Filter Panel (Top) */
.filter-panel-container {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.filter-panel-container.collapsed {
  height: 50px;
}

/* Main Visualization (Center) */
.visualization-container {
  flex: 1;
  background-color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* Data Table (Bottom) */
.data-table-container {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  transition: all 0.3s ease;
  flex-shrink: 0;
  max-height: 40vh;
}

.data-table-container.collapsed {
  height: 50px;
}

/* Panel Headers */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  min-height: 50px;
  box-sizing: border-box;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.collapse-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  color: #6c757d;
  transition: background-color 0.2s ease;
}

.collapse-button:hover {
  background-color: #dee2e6;
  color: #495057;
}

/* Panel Content */
.panel-content {
  padding: 20px;
  overflow-y: auto;
}

.filter-panel-container .panel-content {
  max-height: 200px;
}

.data-table-container .panel-content {
  max-height: calc(40vh - 50px);
}
