import { renderHook } from '@testing-library/react';
import { useFilterOptions } from '../useFilterOptions';

describe('useFilterOptions', () => {
  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useFilterOptions());

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.options.robots).toEqual([]);
    expect(result.current.options.models).toEqual([]);
    expect(result.current.options.methods).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  it('should have correct options structure', () => {
    const { result } = renderHook(() => useFilterOptions());

    expect(result.current.options).toHaveProperty('robots');
    expect(result.current.options).toHaveProperty('models');
    expect(result.current.options).toHaveProperty('methods');
    expect(Array.isArray(result.current.options.robots)).toBe(true);
    expect(Array.isArray(result.current.options.models)).toBe(true);
    expect(Array.isArray(result.current.options.methods)).toBe(true);
  });
});
