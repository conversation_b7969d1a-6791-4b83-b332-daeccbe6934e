import { renderHook } from '@testing-library/react';
import { useVisualizationData } from '../useVisualizationData';

describe('useVisualizationData', () => {

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useVisualizationData());

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);
    expect(typeof result.current.refetch).toBe('function');
  });

  it('should accept initial filters', () => {
    const initialFilters = { method: 'pca', limit: 1 };
    const { result } = renderHook(() => useVisualizationData(initialFilters));

    // Should still initialize correctly
    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('should provide refetch function', () => {
    const { result } = renderHook(() => useVisualizationData());

    expect(typeof result.current.refetch).toBe('function');

    // Should be able to call refetch without crashing
    expect(() => {
      result.current.refetch({ method: 'tsne' });
    }).not.toThrow();
  });


});
