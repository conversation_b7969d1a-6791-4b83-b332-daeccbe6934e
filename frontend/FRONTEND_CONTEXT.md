# Frontend Context

This file provides context on the frontend of the Loom application. This application allows the exploration of large
image datasets via the embeddings of detected objects within those images. The frontend is built using React and
TypeScript.

Whenever you are finished with changes you should make sure to rebuild and restart the frontend container so those
changes are available to the person reviewing your work.

Try and keep text compact to allow for better use of screen real estate.

Favor fewer targeted tests over many nebulous tests.