
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">71.37% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>187/262</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">78.01% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>110/141</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">72.13% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>44/61</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">75.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>178/235</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/components/DataTable"><a href="src/components/DataTable/index.html">src/components/DataTable</a></td>
	<td data-value="75.86" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.86" class="pct medium">75.86%</td>
	<td data-value="29" class="abs medium">22/29</td>
	<td data-value="88.88" class="pct high">88.88%</td>
	<td data-value="18" class="abs high">16/18</td>
	<td data-value="53.84" class="pct medium">53.84%</td>
	<td data-value="13" class="abs medium">7/13</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="28" class="abs medium">21/28</td>
	</tr>

<tr>
	<td class="file high" data-value="src/components/FilterPanel"><a href="src/components/FilterPanel/index.html">src/components/FilterPanel</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="10" class="abs high">8/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="28" class="abs high">28/28</td>
	</tr>

<tr>
	<td class="file high" data-value="src/components/Visualization3D"><a href="src/components/Visualization3D/index.html">src/components/Visualization3D</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	</tr>

<tr>
	<td class="file high" data-value="src/hooks"><a href="src/hooks/index.html">src/hooks</a></td>
	<td data-value="83.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="36" class="abs high">30/36</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="9" class="abs medium">5/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="36" class="abs high">30/36</td>
	</tr>

<tr>
	<td class="file high" data-value="src/mocks"><a href="src/mocks/index.html">src/mocks</a></td>
	<td data-value="96.15" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 96%"></div><div class="cover-empty" style="width: 4%"></div></div>
	</td>
	<td data-value="96.15" class="pct high">96.15%</td>
	<td data-value="26" class="abs high">25/26</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="14" class="abs high">14/14</td>
	<td data-value="88.88" class="pct high">88.88%</td>
	<td data-value="9" class="abs high">8/9</td>
	<td data-value="95.83" class="pct high">95.83%</td>
	<td data-value="24" class="abs high">23/24</td>
	</tr>

<tr>
	<td class="file low" data-value="src/pages"><a href="src/pages/index.html">src/pages</a></td>
	<td data-value="40" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 40%"></div><div class="cover-empty" style="width: 60%"></div></div>
	</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="60" class="abs low">24/60</td>
	<td data-value="84.61" class="pct high">84.61%</td>
	<td data-value="26" class="abs high">22/26</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="10" class="abs low">4/10</td>
	<td data-value="41.37" class="pct low">41.37%</td>
	<td data-value="58" class="abs low">24/58</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/services"><a href="src/services/index.html">src/services</a></td>
	<td data-value="69.23" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.23" class="pct medium">69.23%</td>
	<td data-value="65" class="abs medium">45/65</td>
	<td data-value="63.46" class="pct medium">63.46%</td>
	<td data-value="52" class="abs medium">33/52</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="7" class="abs high">6/7</td>
	<td data-value="89.36" class="pct high">89.36%</td>
	<td data-value="47" class="abs high">42/47</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/test-utils"><a href="src/test-utils/index.html">src/test-utils</a></td>
	<td data-value="71.42" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="7" class="abs medium">5/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="3" class="abs low">1/3</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="6" class="abs high">5/6</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-27T01:53:39.275Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    