{"/app/src/App.tsx": {"path": "/app/src/App.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 2}, "end": {"line": 10, "column": 4}}}, "fnMap": {"0": {"name": "App", "decl": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 12}}, "loc": {"start": {"line": 5, "column": 15}, "end": {"line": 11, "column": 1}}, "line": 5}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/app/src/index.tsx": {"path": "/app/src/index.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 13}, "end": {"line": 8, "column": 1}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 13, "column": 2}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/app/src/components/DataTable/DataTable.tsx": {"path": "/app/src/components/DataTable/DataTable.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 44}, "end": {"line": 115, "column": 1}}, "1": {"start": {"line": 11, "column": 42}, "end": {"line": 11, "column": 74}}, "2": {"start": {"line": 12, "column": 38}, "end": {"line": 12, "column": 60}}, "3": {"start": {"line": 13, "column": 44}, "end": {"line": 13, "column": 75}}, "4": {"start": {"line": 15, "column": 26}, "end": {"line": 23, "column": 3}}, "5": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 45}}, "6": {"start": {"line": 17, "column": 4}, "end": {"line": 21, "column": 5}}, "7": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 29}}, "8": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 26}}, "9": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 33}}, "10": {"start": {"line": 25, "column": 21}, "end": {"line": 32, "column": 3}}, "11": {"start": {"line": 26, "column": 4}, "end": {"line": 31, "column": 5}}, "12": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 65}}, "13": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 28}}, "14": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 30}}, "15": {"start": {"line": 34, "column": 22}, "end": {"line": 37, "column": 3}}, "16": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 43}}, "17": {"start": {"line": 35, "column": 31}, "end": {"line": 35, "column": 43}}, "18": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 47}}, "19": {"start": {"line": 39, "column": 2}, "end": {"line": 114, "column": 4}}, "20": {"start": {"line": 65, "column": 33}, "end": {"line": 65, "column": 49}}, "21": {"start": {"line": 68, "column": 33}, "end": {"line": 68, "column": 48}}, "22": {"start": {"line": 71, "column": 33}, "end": {"line": 71, "column": 48}}, "23": {"start": {"line": 74, "column": 33}, "end": {"line": 74, "column": 48}}, "24": {"start": {"line": 77, "column": 33}, "end": {"line": 77, "column": 59}}, "25": {"start": {"line": 80, "column": 33}, "end": {"line": 80, "column": 61}}, "26": {"start": {"line": 83, "column": 33}, "end": {"line": 83, "column": 53}}, "27": {"start": {"line": 90, "column": 14}, "end": {"line": 108, "column": 19}}, "28": {"start": {"line": 98, "column": 36}, "end": {"line": 98, "column": 61}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 44}, "end": {"line": 10, "column": 45}}, "loc": {"start": {"line": 10, "column": 67}, "end": {"line": 115, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 27}}, "loc": {"start": {"line": 15, "column": 42}, "end": {"line": 23, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 21}, "end": {"line": 25, "column": 22}}, "loc": {"start": {"line": 25, "column": 41}, "end": {"line": 32, "column": 3}}, "line": 25}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 34, "column": 22}, "end": {"line": 34, "column": 23}}, "loc": {"start": {"line": 34, "column": 42}, "end": {"line": 37, "column": 3}}, "line": 34}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 65, "column": 27}, "end": {"line": 65, "column": 28}}, "loc": {"start": {"line": 65, "column": 33}, "end": {"line": 65, "column": 49}}, "line": 65}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 68, "column": 27}, "end": {"line": 68, "column": 28}}, "loc": {"start": {"line": 68, "column": 33}, "end": {"line": 68, "column": 48}}, "line": 68}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 28}}, "loc": {"start": {"line": 71, "column": 33}, "end": {"line": 71, "column": 48}}, "line": 71}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 74, "column": 27}, "end": {"line": 74, "column": 28}}, "loc": {"start": {"line": 74, "column": 33}, "end": {"line": 74, "column": 48}}, "line": 74}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 77, "column": 27}, "end": {"line": 77, "column": 28}}, "loc": {"start": {"line": 77, "column": 33}, "end": {"line": 77, "column": 59}}, "line": 77}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 80, "column": 27}, "end": {"line": 80, "column": 28}}, "loc": {"start": {"line": 80, "column": 33}, "end": {"line": 80, "column": 61}}, "line": 80}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 83, "column": 27}, "end": {"line": 83, "column": 28}}, "loc": {"start": {"line": 83, "column": 33}, "end": {"line": 83, "column": 53}}, "line": 83}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 89, "column": 22}, "end": {"line": 89, "column": 23}}, "loc": {"start": {"line": 90, "column": 14}, "end": {"line": 108, "column": 19}}, "line": 90}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 98, "column": 30}, "end": {"line": 98, "column": 31}}, "loc": {"start": {"line": 98, "column": 36}, "end": {"line": 98, "column": 61}}, "line": 98}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 21, "column": 5}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 21, "column": 5}}, {"start": {"line": 19, "column": 11}, "end": {"line": 21, "column": 5}}], "line": 17}, "1": {"loc": {"start": {"line": 26, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 26, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 26}, "2": {"loc": {"start": {"line": 27, "column": 23}, "end": {"line": 27, "column": 63}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 49}, "end": {"line": 27, "column": 55}}, {"start": {"line": 27, "column": 58}, "end": {"line": 27, "column": 63}}], "line": 27}, "3": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 43}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 43}}, {"start": {}, "end": {}}], "line": 35}, "4": {"loc": {"start": {"line": 36, "column": 11}, "end": {"line": 36, "column": 46}}, "type": "cond-expr", "locations": [{"start": {"line": 36, "column": 37}, "end": {"line": 36, "column": 40}}, {"start": {"line": 36, "column": 43}, "end": {"line": 36, "column": 46}}], "line": 36}, "5": {"loc": {"start": {"line": 44, "column": 13}, "end": {"line": 44, "column": 69}}, "type": "cond-expr", "locations": [{"start": {"line": 44, "column": 23}, "end": {"line": 44, "column": 35}}, {"start": {"line": 44, "column": 38}, "end": {"line": 44, "column": 69}}], "line": 44}, "6": {"loc": {"start": {"line": 46, "column": 11}, "end": {"line": 50, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 46, "column": 11}, "end": {"line": 46, "column": 32}}, {"start": {"line": 47, "column": 12}, "end": {"line": 49, "column": 19}}], "line": 46}, "7": {"loc": {"start": {"line": 92, "column": 27}, "end": {"line": 92, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 92, "column": 56}, "end": {"line": 92, "column": 66}}, {"start": {"line": 92, "column": 69}, "end": {"line": 92, "column": 71}}], "line": 92}, "8": {"loc": {"start": {"line": 104, "column": 21}, "end": {"line": 104, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 104, "column": 21}, "end": {"line": 104, "column": 40}}, {"start": {"line": 104, "column": 44}, "end": {"line": 104, "column": 49}}], "line": 104}}, "s": {"0": 2, "1": 44, "2": 44, "3": 44, "4": 44, "5": 4, "6": 4, "7": 0, "8": 4, "9": 4, "10": 44, "11": 0, "12": 0, "13": 0, "14": 0, "15": 44, "16": 308, "17": 264, "18": 44, "19": 44, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 31, "28": 4}, "f": {"0": 44, "1": 4, "2": 0, "3": 308, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 31, "12": 4}, "b": {"0": [0, 4], "1": [0, 0], "2": [0, 0], "3": [264, 44], "4": [44, 0], "5": [14, 30], "6": [44, 4], "7": [5, 26], "8": [31, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "88831b385f706685e79c2d5e3eea320bce3f903a"}, "/app/src/components/FilterPanel/FilterPanel.tsx": {"path": "/app/src/components/FilterPanel/FilterPanel.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 48}, "end": {"line": 152, "column": 1}}, "1": {"start": {"line": 24, "column": 36}, "end": {"line": 24, "column": 50}}, "2": {"start": {"line": 25, "column": 38}, "end": {"line": 25, "column": 50}}, "3": {"start": {"line": 26, "column": 42}, "end": {"line": 26, "column": 57}}, "4": {"start": {"line": 27, "column": 46}, "end": {"line": 27, "column": 58}}, "5": {"start": {"line": 28, "column": 44}, "end": {"line": 28, "column": 56}}, "6": {"start": {"line": 31, "column": 2}, "end": {"line": 35, "column": 23}}, "7": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 68}}, "8": {"start": {"line": 32, "column": 30}, "end": {"line": 32, "column": 68}}, "9": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 72}}, "10": {"start": {"line": 33, "column": 31}, "end": {"line": 33, "column": 72}}, "11": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 75}}, "12": {"start": {"line": 34, "column": 33}, "end": {"line": 34, "column": 75}}, "13": {"start": {"line": 39, "column": 32}, "end": {"line": 41, "column": 3}}, "14": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 45}}, "15": {"start": {"line": 43, "column": 28}, "end": {"line": 47, "column": 3}}, "16": {"start": {"line": 44, "column": 21}, "end": {"line": 44, "column": 45}}, "17": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 30}}, "18": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 60}}, "19": {"start": {"line": 49, "column": 29}, "end": {"line": 53, "column": 3}}, "20": {"start": {"line": 50, "column": 22}, "end": {"line": 50, "column": 36}}, "21": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 33}}, "22": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 75}}, "23": {"start": {"line": 55, "column": 28}, "end": {"line": 59, "column": 3}}, "24": {"start": {"line": 56, "column": 21}, "end": {"line": 56, "column": 35}}, "25": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 31}}, "26": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 76}}, "27": {"start": {"line": 60, "column": 2}, "end": {"line": 151, "column": 4}}, "28": {"start": {"line": 102, "column": 14}, "end": {"line": 102, "column": 67}}, "29": {"start": {"line": 118, "column": 14}, "end": {"line": 118, "column": 64}}, "30": {"start": {"line": 131, "column": 29}, "end": {"line": 131, "column": 58}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 18, "column": 48}, "end": {"line": 18, "column": 49}}, "loc": {"start": {"line": 23, "column": 6}, "end": {"line": 152, "column": 1}}, "line": 23}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 31, "column": 12}, "end": {"line": 31, "column": 13}}, "loc": {"start": {"line": 31, "column": 18}, "end": {"line": 35, "column": 3}}, "line": 31}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 32}, "end": {"line": 39, "column": 33}}, "loc": {"start": {"line": 39, "column": 76}, "end": {"line": 41, "column": 3}}, "line": 39}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 43, "column": 28}, "end": {"line": 43, "column": 29}}, "loc": {"start": {"line": 43, "column": 73}, "end": {"line": 47, "column": 3}}, "line": 43}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 49, "column": 29}, "end": {"line": 49, "column": 30}}, "loc": {"start": {"line": 49, "column": 74}, "end": {"line": 53, "column": 3}}, "line": 49}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 55, "column": 28}, "end": {"line": 55, "column": 29}}, "loc": {"start": {"line": 55, "column": 73}, "end": {"line": 59, "column": 3}}, "line": 55}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 101, "column": 39}, "end": {"line": 101, "column": 40}}, "loc": {"start": {"line": 102, "column": 14}, "end": {"line": 102, "column": 67}}, "line": 102}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 117, "column": 38}, "end": {"line": 117, "column": 39}}, "loc": {"start": {"line": 118, "column": 14}, "end": {"line": 118, "column": 64}}, "line": 118}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 131, "column": 22}, "end": {"line": 131, "column": 23}}, "loc": {"start": {"line": 131, "column": 29}, "end": {"line": 131, "column": 58}}, "line": 131}}, "branchMap": {"0": {"loc": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 68}}, "type": "if", "locations": [{"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 68}}, {"start": {}, "end": {}}], "line": 32}, "1": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 72}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 72}}, {"start": {}, "end": {}}], "line": 33}, "2": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 75}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 75}}, {"start": {}, "end": {}}], "line": 34}, "3": {"loc": {"start": {"line": 52, "column": 49}, "end": {"line": 52, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 49}, "end": {"line": 52, "column": 58}}, {"start": {"line": 52, "column": 62}, "end": {"line": 52, "column": 71}}], "line": 52}, "4": {"loc": {"start": {"line": 58, "column": 51}, "end": {"line": 58, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 51}, "end": {"line": 58, "column": 59}}, {"start": {"line": 58, "column": 63}, "end": {"line": 58, "column": 72}}], "line": 58}}, "s": {"0": 2, "1": 49, "2": 49, "3": 49, "4": 49, "5": 49, "6": 49, "7": 26, "8": 2, "9": 26, "10": 2, "11": 26, "12": 1, "13": 49, "14": 2, "15": 49, "16": 2, "17": 2, "18": 2, "19": 49, "20": 2, "21": 2, "22": 2, "23": 49, "24": 1, "25": 1, "26": 1, "27": 49, "28": 78, "29": 78, "30": 1}, "f": {"0": 49, "1": 26, "2": 2, "3": 2, "4": 2, "5": 1, "6": 78, "7": 78, "8": 1}, "b": {"0": [2, 24], "1": [2, 24], "2": [1, 25], "3": [2, 0], "4": [1, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f45b190f6ee6f5742932a13107a1647a6a736b94"}, "/app/src/components/Visualization3D/Visualization3D.tsx": {"path": "/app/src/components/Visualization3D/Visualization3D.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 56}, "end": {"line": 60, "column": 1}}, "1": {"start": {"line": 12, "column": 2}, "end": {"line": 26, "column": 3}}, "2": {"start": {"line": 13, "column": 4}, "end": {"line": 25, "column": 6}}, "3": {"start": {"line": 19, "column": 62}, "end": {"line": 19, "column": 86}}, "4": {"start": {"line": 28, "column": 2}, "end": {"line": 59, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 56}, "end": {"line": 11, "column": 57}}, "loc": {"start": {"line": 11, "column": 86}, "end": {"line": 60, "column": 1}}, "line": 11}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 56}, "end": {"line": 19, "column": 57}}, "loc": {"start": {"line": 19, "column": 62}, "end": {"line": 19, "column": 86}}, "line": 19}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 26, "column": 3}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 26, "column": 3}}, {"start": {}, "end": {}}], "line": 12}, "1": {"loc": {"start": {"line": 34, "column": 13}, "end": {"line": 34, "column": 92}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 20}, "end": {"line": 34, "column": 50}}, {"start": {"line": 34, "column": 53}, "end": {"line": 34, "column": 92}}], "line": 34}, "2": {"loc": {"start": {"line": 39, "column": 44}, "end": {"line": 39, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 44}, "end": {"line": 39, "column": 62}}, {"start": {"line": 39, "column": 66}, "end": {"line": 39, "column": 67}}], "line": 39}, "3": {"loc": {"start": {"line": 43, "column": 44}, "end": {"line": 43, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 43, "column": 44}, "end": {"line": 43, "column": 56}}, {"start": {"line": 43, "column": 60}, "end": {"line": 43, "column": 66}}], "line": 43}, "4": {"loc": {"start": {"line": 47, "column": 44}, "end": {"line": 47, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 44}, "end": {"line": 47, "column": 58}}, {"start": {"line": 47, "column": 62}, "end": {"line": 47, "column": 68}}], "line": 47}, "5": {"loc": {"start": {"line": 54, "column": 57}, "end": {"line": 54, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 54, "column": 67}, "end": {"line": 54, "column": 73}}, {"start": {"line": 54, "column": 76}, "end": {"line": 54, "column": 82}}], "line": 54}}, "s": {"0": 2, "1": 39, "2": 3, "3": 1, "4": 36}, "f": {"0": 39, "1": 1}, "b": {"0": [3, 36], "1": [8, 28], "2": [36, 29], "3": [36, 29], "4": [36, 29], "5": [15, 21]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "9069af8fe9cf1641c64253a018fa78d332d93072"}, "/app/src/hooks/useFilterOptions.ts": {"path": "/app/src/hooks/useFilterOptions.ts", "statementMap": {"0": {"start": {"line": 16, "column": 32}, "end": {"line": 59, "column": 1}}, "1": {"start": {"line": 17, "column": 32}, "end": {"line": 21, "column": 4}}, "2": {"start": {"line": 22, "column": 32}, "end": {"line": 22, "column": 46}}, "3": {"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 57}}, "4": {"start": {"line": 25, "column": 2}, "end": {"line": 52, "column": 9}}, "5": {"start": {"line": 26, "column": 25}, "end": {"line": 49, "column": 5}}, "6": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 23}}, "7": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 21}}, "8": {"start": {"line": 30, "column": 6}, "end": {"line": 48, "column": 7}}, "9": {"start": {"line": 31, "column": 42}, "end": {"line": 35, "column": 10}}, "10": {"start": {"line": 37, "column": 8}, "end": {"line": 41, "column": 11}}, "11": {"start": {"line": 43, "column": 29}, "end": {"line": 43, "column": 98}}, "12": {"start": {"line": 44, "column": 8}, "end": {"line": 44, "column": 31}}, "13": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 61}}, "14": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 26}}, "15": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 19}}, "16": {"start": {"line": 54, "column": 2}, "end": {"line": 58, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 16, "column": 32}, "end": {"line": 16, "column": 33}}, "loc": {"start": {"line": 16, "column": 62}, "end": {"line": 59, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 12}, "end": {"line": 25, "column": 13}}, "loc": {"start": {"line": 25, "column": 18}, "end": {"line": 52, "column": 3}}, "line": 25}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 26}}, "loc": {"start": {"line": 26, "column": 37}, "end": {"line": 49, "column": 5}}, "line": 26}}, "branchMap": {"0": {"loc": {"start": {"line": 43, "column": 29}, "end": {"line": 43, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 43, "column": 52}, "end": {"line": 43, "column": 63}}, {"start": {"line": 43, "column": 66}, "end": {"line": 43, "column": 98}}], "line": 43}}, "s": {"0": 2, "1": 37, "2": 37, "3": 37, "4": 37, "5": 14, "6": 14, "7": 14, "8": 14, "9": 14, "10": 10, "11": 2, "12": 2, "13": 2, "14": 12, "15": 14, "16": 37}, "f": {"0": 37, "1": 14, "2": 14}, "b": {"0": [2, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "17e0541a04d58f5bfd78c2926808f5a9924548a6"}, "/app/src/hooks/useVisualizationData.ts": {"path": "/app/src/hooks/useVisualizationData.ts", "statementMap": {"0": {"start": {"line": 11, "column": 36}, "end": {"line": 54, "column": 1}}, "1": {"start": {"line": 14, "column": 26}, "end": {"line": 14, "column": 70}}, "2": {"start": {"line": 15, "column": 32}, "end": {"line": 15, "column": 47}}, "3": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 57}}, "4": {"start": {"line": 18, "column": 20}, "end": {"line": 37, "column": 22}}, "5": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 21}}, "6": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 19}}, "7": {"start": {"line": 22, "column": 4}, "end": {"line": 36, "column": 5}}, "8": {"start": {"line": 23, "column": 23}, "end": {"line": 28, "column": 8}}, "9": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 24}}, "10": {"start": {"line": 31, "column": 27}, "end": {"line": 31, "column": 100}}, "11": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 29}}, "12": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 63}}, "13": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 24}}, "14": {"start": {"line": 39, "column": 18}, "end": {"line": 41, "column": 17}}, "15": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 29}}, "16": {"start": {"line": 44, "column": 2}, "end": {"line": 46, "column": 18}}, "17": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 16}}, "18": {"start": {"line": 48, "column": 2}, "end": {"line": 53, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 36}, "end": {"line": 11, "column": 37}}, "loc": {"start": {"line": 13, "column": 33}, "end": {"line": 54, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 32}, "end": {"line": 18, "column": 33}}, "loc": {"start": {"line": 18, "column": 78}, "end": {"line": 37, "column": 3}}, "line": 18}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 30}, "end": {"line": 39, "column": 31}}, "loc": {"start": {"line": 39, "column": 72}, "end": {"line": 41, "column": 3}}, "line": 39}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 44, "column": 12}, "end": {"line": 44, "column": 13}}, "loc": {"start": {"line": 44, "column": 18}, "end": {"line": 46, "column": 3}}, "line": 44}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 43}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 41}, "end": {"line": 12, "column": 43}}], "line": 12}, "1": {"loc": {"start": {"line": 18, "column": 39}, "end": {"line": 18, "column": 73}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 71}, "end": {"line": 18, "column": 73}}], "line": 18}, "2": {"loc": {"start": {"line": 27, "column": 15}, "end": {"line": 27, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 15}, "end": {"line": 27, "column": 28}}, {"start": {"line": 27, "column": 32}, "end": {"line": 27, "column": 52}}, {"start": {"line": 27, "column": 56}, "end": {"line": 27, "column": 61}}], "line": 27}, "3": {"loc": {"start": {"line": 31, "column": 27}, "end": {"line": 31, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 31, "column": 50}, "end": {"line": 31, "column": 61}}, {"start": {"line": 31, "column": 64}, "end": {"line": 31, "column": 100}}], "line": 31}}, "s": {"0": 2, "1": 869, "2": 869, "3": 869, "4": 869, "5": 573, "6": 573, "7": 573, "8": 573, "9": 439, "10": 130, "11": 130, "12": 130, "13": 569, "14": 869, "15": 2, "16": 869, "17": 571, "18": 869}, "f": {"0": 869, "1": 573, "2": 2, "3": 571}, "b": {"0": [837], "1": [571], "2": [573, 572, 570], "3": [130, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1202c4297ca1fb072571997622e302b6ff46963a"}, "/app/src/mocks/handlers.ts": {"path": "/app/src/mocks/handlers.ts", "statementMap": {"0": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 44}}, "1": {"start": {"line": 7, "column": 48}, "end": {"line": 59, "column": 1}}, "2": {"start": {"line": 61, "column": 57}, "end": {"line": 71, "column": 1}}, "3": {"start": {"line": 73, "column": 44}, "end": {"line": 89, "column": 1}}, "4": {"start": {"line": 91, "column": 24}, "end": {"line": 151, "column": 1}}, "5": {"start": {"line": 94, "column": 19}, "end": {"line": 94, "column": 53}}, "6": {"start": {"line": 95, "column": 21}, "end": {"line": 95, "column": 57}}, "7": {"start": {"line": 96, "column": 18}, "end": {"line": 96, "column": 51}}, "8": {"start": {"line": 99, "column": 25}, "end": {"line": 99, "column": 49}}, "9": {"start": {"line": 101, "column": 4}, "end": {"line": 103, "column": 5}}, "10": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": 79}}, "11": {"start": {"line": 102, "column": 54}, "end": {"line": 102, "column": 77}}, "12": {"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 5}}, "13": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 83}}, "14": {"start": {"line": 106, "column": 54}, "end": {"line": 106, "column": 81}}, "15": {"start": {"line": 109, "column": 4}, "end": {"line": 111, "column": 5}}, "16": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 64}}, "17": {"start": {"line": 113, "column": 44}, "end": {"line": 119, "column": 5}}, "18": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 35}}, "19": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": 45}}, "20": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 66}}, "21": {"start": {"line": 135, "column": 4}, "end": {"line": 135, "column": 70}}, "22": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": 50}}, "23": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": 43}}, "24": {"start": {"line": 149, "column": 4}, "end": {"line": 149, "column": 78}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 93, "column": 58}, "end": {"line": 93, "column": 59}}, "loc": {"start": {"line": 93, "column": 77}, "end": {"line": 122, "column": 3}}, "line": 93}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 102, "column": 45}, "end": {"line": 102, "column": 46}}, "loc": {"start": {"line": 102, "column": 54}, "end": {"line": 102, "column": 77}}, "line": 102}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 106, "column": 45}, "end": {"line": 106, "column": 46}}, "loc": {"start": {"line": 106, "column": 54}, "end": {"line": 106, "column": 81}}, "line": 106}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 125, "column": 60}, "end": {"line": 125, "column": 61}}, "loc": {"start": {"line": 125, "column": 79}, "end": {"line": 127, "column": 3}}, "line": 125}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 130, "column": 59}, "end": {"line": 130, "column": 60}}, "loc": {"start": {"line": 130, "column": 78}, "end": {"line": 132, "column": 3}}, "line": 130}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 134, "column": 59}, "end": {"line": 134, "column": 60}}, "loc": {"start": {"line": 134, "column": 78}, "end": {"line": 136, "column": 3}}, "line": 134}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 138, "column": 60}, "end": {"line": 138, "column": 61}}, "loc": {"start": {"line": 138, "column": 79}, "end": {"line": 140, "column": 3}}, "line": 138}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 143, "column": 37}, "end": {"line": 143, "column": 38}}, "loc": {"start": {"line": 143, "column": 56}, "end": {"line": 145, "column": 3}}, "line": 143}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 148, "column": 64}, "end": {"line": 148, "column": 65}}, "loc": {"start": {"line": 148, "column": 83}, "end": {"line": 150, "column": 3}}, "line": 148}}, "branchMap": {"0": {"loc": {"start": {"line": 101, "column": 4}, "end": {"line": 103, "column": 5}}, "type": "if", "locations": [{"start": {"line": 101, "column": 4}, "end": {"line": 103, "column": 5}}, {"start": {}, "end": {}}], "line": 101}, "1": {"loc": {"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 31}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 8}, "end": {"line": 101, "column": 14}}, {"start": {"line": 101, "column": 18}, "end": {"line": 101, "column": 31}}], "line": 101}, "2": {"loc": {"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 5}}, "type": "if", "locations": [{"start": {"line": 105, "column": 4}, "end": {"line": 107, "column": 5}}, {"start": {}, "end": {}}], "line": 105}, "3": {"loc": {"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 8}, "end": {"line": 105, "column": 16}}, {"start": {"line": 105, "column": 20}, "end": {"line": 105, "column": 35}}], "line": 105}, "4": {"loc": {"start": {"line": 109, "column": 4}, "end": {"line": 111, "column": 5}}, "type": "if", "locations": [{"start": {"line": 109, "column": 4}, "end": {"line": 111, "column": 5}}, {"start": {}, "end": {}}], "line": 109}, "5": {"loc": {"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 14}, "end": {"line": 115, "column": 20}}, {"start": {"line": 115, "column": 24}, "end": {"line": 115, "column": 29}}], "line": 115}, "6": {"loc": {"start": {"line": 116, "column": 16}, "end": {"line": 116, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 116, "column": 16}, "end": {"line": 116, "column": 24}}, {"start": {"line": 116, "column": 28}, "end": {"line": 116, "column": 38}}], "line": 116}}, "s": {"0": 7, "1": 7, "2": 7, "3": 7, "4": 7, "5": 441, "6": 441, "7": 441, "8": 441, "9": 441, "10": 4, "11": 12, "12": 441, "13": 1, "14": 2, "15": 441, "16": 440, "17": 441, "18": 441, "19": 1, "20": 11, "21": 12, "22": 12, "23": 1, "24": 0}, "f": {"0": 441, "1": 12, "2": 2, "3": 1, "4": 11, "5": 12, "6": 12, "7": 1, "8": 0}, "b": {"0": [4, 437], "1": [441, 4], "2": [1, 440], "3": [441, 1], "4": [440, 1], "5": [441, 437], "6": [441, 440]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "14861ba306a8c7950f07d5a1176d597dd2e1378a"}, "/app/src/mocks/server.ts": {"path": "/app/src/mocks/server.ts", "statementMap": {"0": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 46}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 7}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "0b7211ed5114c928781727353a7eff3e87202bd8"}, "/app/src/pages/MainPage.tsx": {"path": "/app/src/pages/MainPage.tsx", "statementMap": {"0": {"start": {"line": 10, "column": 27}, "end": {"line": 158, "column": 1}}, "1": {"start": {"line": 11, "column": 62}, "end": {"line": 11, "column": 77}}, "2": {"start": {"line": 12, "column": 58}, "end": {"line": 12, "column": 73}}, "3": {"start": {"line": 13, "column": 52}, "end": {"line": 13, "column": 65}}, "4": {"start": {"line": 14, "column": 48}, "end": {"line": 14, "column": 61}}, "5": {"start": {"line": 15, "column": 46}, "end": {"line": 15, "column": 80}}, "6": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 40}}, "7": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 39}}, "8": {"start": {"line": 19, "column": 17}, "end": {"line": 19, "column": 26}}, "9": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 31}}, "10": {"start": {"line": 23, "column": 87}, "end": {"line": 23, "column": 123}}, "11": {"start": {"line": 24, "column": 62}, "end": {"line": 24, "column": 80}}, "12": {"start": {"line": 27, "column": 30}, "end": {"line": 30, "column": 15}}, "13": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 34}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 24}}, "15": {"start": {"line": 32, "column": 29}, "end": {"line": 37, "column": 8}}, "16": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 42}}, "17": {"start": {"line": 33, "column": 35}, "end": {"line": 33, "column": 42}}, "18": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 45}}, "19": {"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 79}}, "20": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 36}}, "21": {"start": {"line": 39, "column": 32}, "end": {"line": 45, "column": 8}}, "22": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 37}}, "23": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 36}}, "24": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 40}}, "25": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 66}}, "26": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 67}}, "27": {"start": {"line": 47, "column": 34}, "end": {"line": 56, "column": 68}}, "28": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 36}}, "29": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 31}}, "30": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 44}}, "31": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 45}}, "32": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 44}}, "33": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 63}}, "34": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 64}}, "35": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 23}}, "36": {"start": {"line": 58, "column": 28}, "end": {"line": 63, "column": 8}}, "37": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 41}}, "38": {"start": {"line": 59, "column": 34}, "end": {"line": 59, "column": 41}}, "39": {"start": {"line": 60, "column": 19}, "end": {"line": 60, "column": 45}}, "40": {"start": {"line": 61, "column": 22}, "end": {"line": 61, "column": 79}}, "41": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 34}}, "42": {"start": {"line": 65, "column": 31}, "end": {"line": 71, "column": 8}}, "43": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 36}}, "44": {"start": {"line": 67, "column": 4}, "end": {"line": 67, "column": 36}}, "45": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 40}}, "46": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 65}}, "47": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 66}}, "48": {"start": {"line": 73, "column": 33}, "end": {"line": 82, "column": 64}}, "49": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 35}}, "50": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 31}}, "51": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 42}}, "52": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 45}}, "53": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 44}}, "54": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 62}}, "55": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 63}}, "56": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": 23}}, "57": {"start": {"line": 84, "column": 2}, "end": {"line": 157, "column": 4}}, "58": {"start": {"line": 95, "column": 27}, "end": {"line": 95, "column": 77}}, "59": {"start": {"line": 142, "column": 27}, "end": {"line": 142, "column": 73}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 10, "column": 27}, "end": {"line": 10, "column": 28}}, "loc": {"start": {"line": 10, "column": 33}, "end": {"line": 158, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 42}, "end": {"line": 27, "column": 43}}, "loc": {"start": {"line": 27, "column": 80}, "end": {"line": 30, "column": 3}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 41}, "end": {"line": 32, "column": 42}}, "loc": {"start": {"line": 32, "column": 60}, "end": {"line": 37, "column": 3}}, "line": 32}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 44}, "end": {"line": 39, "column": 45}}, "loc": {"start": {"line": 39, "column": 50}, "end": {"line": 45, "column": 3}}, "line": 39}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 47, "column": 46}, "end": {"line": 47, "column": 47}}, "loc": {"start": {"line": 47, "column": 71}, "end": {"line": 56, "column": 3}}, "line": 47}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 58, "column": 40}, "end": {"line": 58, "column": 41}}, "loc": {"start": {"line": 58, "column": 59}, "end": {"line": 63, "column": 3}}, "line": 58}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 65, "column": 43}, "end": {"line": 65, "column": 44}}, "loc": {"start": {"line": 65, "column": 49}, "end": {"line": 71, "column": 3}}, "line": 65}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 73, "column": 45}, "end": {"line": 73, "column": 46}}, "loc": {"start": {"line": 73, "column": 70}, "end": {"line": 82, "column": 3}}, "line": 73}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 95, "column": 21}, "end": {"line": 95, "column": 22}}, "loc": {"start": {"line": 95, "column": 27}, "end": {"line": 95, "column": 77}}, "line": 95}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 142, "column": 21}, "end": {"line": 142, "column": 22}}, "loc": {"start": {"line": 142, "column": 27}, "end": {"line": 142, "column": 73}}, "line": 142}}, "branchMap": {"0": {"loc": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 42}}, "type": "if", "locations": [{"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 42}}, {"start": {}, "end": {}}], "line": 33}, "1": {"loc": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 41}}, "type": "if", "locations": [{"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 41}}, {"start": {}, "end": {}}], "line": 59}, "2": {"loc": {"start": {"line": 88, "column": 45}, "end": {"line": 88, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 88, "column": 70}, "end": {"line": 88, "column": 81}}, {"start": {"line": 88, "column": 84}, "end": {"line": 88, "column": 86}}], "line": 88}, "3": {"loc": {"start": {"line": 89, "column": 25}, "end": {"line": 89, "column": 83}}, "type": "cond-expr", "locations": [{"start": {"line": 89, "column": 50}, "end": {"line": 89, "column": 56}}, {"start": {"line": 89, "column": 59}, "end": {"line": 89, "column": 83}}], "line": 89}, "4": {"loc": {"start": {"line": 97, "column": 13}, "end": {"line": 97, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 97, "column": 38}, "end": {"line": 97, "column": 41}}, {"start": {"line": 97, "column": 44}, "end": {"line": 97, "column": 47}}], "line": 97}, "5": {"loc": {"start": {"line": 100, "column": 9}, "end": {"line": 109, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 9}, "end": {"line": 100, "column": 32}}, {"start": {"line": 101, "column": 10}, "end": {"line": 108, "column": 16}}], "line": 100}, "6": {"loc": {"start": {"line": 110, "column": 9}, "end": {"line": 115, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 9}, "end": {"line": 110, "column": 32}}, {"start": {"line": 111, "column": 10}, "end": {"line": 114, "column": 12}}], "line": 110}, "7": {"loc": {"start": {"line": 129, "column": 43}, "end": {"line": 129, "column": 82}}, "type": "cond-expr", "locations": [{"start": {"line": 129, "column": 66}, "end": {"line": 129, "column": 77}}, {"start": {"line": 129, "column": 80}, "end": {"line": 129, "column": 82}}], "line": 129}, "8": {"loc": {"start": {"line": 130, "column": 25}, "end": {"line": 130, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 48}, "end": {"line": 130, "column": 54}}, {"start": {"line": 130, "column": 57}, "end": {"line": 130, "column": 79}}], "line": 130}, "9": {"loc": {"start": {"line": 132, "column": 9}, "end": {"line": 137, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 132, "column": 9}, "end": {"line": 132, "column": 30}}, {"start": {"line": 133, "column": 10}, "end": {"line": 136, "column": 12}}], "line": 132}, "10": {"loc": {"start": {"line": 144, "column": 13}, "end": {"line": 144, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 144, "column": 36}, "end": {"line": 144, "column": 39}}, {"start": {"line": 144, "column": 42}, "end": {"line": 144, "column": 45}}], "line": 144}, "11": {"loc": {"start": {"line": 147, "column": 9}, "end": {"line": 154, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 147, "column": 9}, "end": {"line": 147, "column": 30}}, {"start": {"line": 148, "column": 10}, "end": {"line": 153, "column": 16}}], "line": 147}, "12": {"loc": {"start": {"line": 150, "column": 20}, "end": {"line": 150, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 150, "column": 20}, "end": {"line": 150, "column": 45}}, {"start": {"line": 150, "column": 49}, "end": {"line": 150, "column": 51}}], "line": 150}}, "s": {"0": 1, "1": 29, "2": 29, "3": 29, "4": 29, "5": 29, "6": 29, "7": 29, "8": 29, "9": 29, "10": 29, "11": 29, "12": 29, "13": 2, "14": 2, "15": 29, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 29, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 29, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 29, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 29, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 29, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 29, "58": 1, "59": 0}, "f": {"0": 29, "1": 2, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 1, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [1, 28], "3": [1, 28], "4": [1, 28], "5": [29, 28], "6": [29, 28], "7": [0, 29], "8": [0, 29], "9": [29, 29], "10": [0, 29], "11": [29, 29], "12": [29, 23]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d9cbd507b26822589f106847e0a6c69a14cf6ffe"}, "/app/src/services/api.ts": {"path": "/app/src/services/api.ts", "statementMap": {"0": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 77}}, "1": {"start": {"line": 66, "column": 19}, "end": {"line": 156, "column": 1}}, "2": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 40}}, "3": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 64}}, "4": {"start": {"line": 71, "column": 24}, "end": {"line": 71, "column": 64}}, "5": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 70}}, "6": {"start": {"line": 72, "column": 26}, "end": {"line": 72, "column": 70}}, "7": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 72}}, "8": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": 72}}, "9": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 86}}, "10": {"start": {"line": 74, "column": 37}, "end": {"line": 74, "column": 86}}, "11": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 86}}, "12": {"start": {"line": 75, "column": 37}, "end": {"line": 75, "column": 86}}, "13": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 86}}, "14": {"start": {"line": 76, "column": 37}, "end": {"line": 76, "column": 86}}, "15": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 86}}, "16": {"start": {"line": 77, "column": 37}, "end": {"line": 77, "column": 86}}, "17": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 86}}, "18": {"start": {"line": 78, "column": 37}, "end": {"line": 78, "column": 86}}, "19": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 86}}, "20": {"start": {"line": 79, "column": 37}, "end": {"line": 79, "column": 86}}, "21": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "22": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 70}}, "23": {"start": {"line": 81, "column": 38}, "end": {"line": 81, "column": 68}}, "24": {"start": {"line": 84, "column": 21}, "end": {"line": 84, "column": 89}}, "25": {"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 5}}, "26": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 84}}, "27": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 27}}, "28": {"start": {"line": 105, "column": 19}, "end": {"line": 105, "column": 40}}, "29": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 69}}, "30": {"start": {"line": 107, "column": 22}, "end": {"line": 107, "column": 69}}, "31": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 72}}, "32": {"start": {"line": 108, "column": 23}, "end": {"line": 108, "column": 72}}, "33": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 86}}, "34": {"start": {"line": 109, "column": 37}, "end": {"line": 109, "column": 86}}, "35": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 86}}, "36": {"start": {"line": 110, "column": 37}, "end": {"line": 110, "column": 86}}, "37": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 86}}, "38": {"start": {"line": 111, "column": 37}, "end": {"line": 111, "column": 86}}, "39": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 86}}, "40": {"start": {"line": 112, "column": 37}, "end": {"line": 112, "column": 86}}, "41": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 101}}, "42": {"start": {"line": 113, "column": 42}, "end": {"line": 113, "column": 101}}, "43": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 101}}, "44": {"start": {"line": 114, "column": 42}, "end": {"line": 114, "column": 101}}, "45": {"start": {"line": 116, "column": 21}, "end": {"line": 116, "column": 93}}, "46": {"start": {"line": 117, "column": 4}, "end": {"line": 119, "column": 5}}, "47": {"start": {"line": 118, "column": 6}, "end": {"line": 118, "column": 72}}, "48": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": 27}}, "49": {"start": {"line": 125, "column": 21}, "end": {"line": 125, "column": 80}}, "50": {"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": 5}}, "51": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 72}}, "52": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 27}}, "53": {"start": {"line": 133, "column": 21}, "end": {"line": 133, "column": 80}}, "54": {"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, "55": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 72}}, "56": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 27}}, "57": {"start": {"line": 141, "column": 21}, "end": {"line": 141, "column": 81}}, "58": {"start": {"line": 142, "column": 4}, "end": {"line": 144, "column": 5}}, "59": {"start": {"line": 143, "column": 6}, "end": {"line": 143, "column": 73}}, "60": {"start": {"line": 145, "column": 4}, "end": {"line": 145, "column": 27}}, "61": {"start": {"line": 150, "column": 21}, "end": {"line": 150, "column": 58}}, "62": {"start": {"line": 151, "column": 4}, "end": {"line": 153, "column": 5}}, "63": {"start": {"line": 152, "column": 6}, "end": {"line": 152, "column": 69}}, "64": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 27}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": 3}}, "loc": {"start": {"line": 68, "column": 97}, "end": {"line": 89, "column": 3}}, "line": 68}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 81, "column": 32}, "end": {"line": 81, "column": 33}}, "loc": {"start": {"line": 81, "column": 38}, "end": {"line": 81, "column": 68}}, "line": 81}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 3}}, "loc": {"start": {"line": 104, "column": 30}, "end": {"line": 121, "column": 3}}, "line": 104}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 3}}, "loc": {"start": {"line": 124, "column": 48}, "end": {"line": 130, "column": 3}}, "line": 124}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 3}}, "loc": {"start": {"line": 132, "column": 48}, "end": {"line": 138, "column": 3}}, "line": 132}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 3}}, "loc": {"start": {"line": 140, "column": 49}, "end": {"line": 146, "column": 3}}, "line": 140}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 149, "column": 2}, "end": {"line": 149, "column": 3}}, "loc": {"start": {"line": 149, "column": 51}, "end": {"line": 155, "column": 3}}, "line": 149}}, "branchMap": {"0": {"loc": {"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 77}}, "type": "binary-expr", "locations": [{"start": {"line": 3, "column": 21}, "end": {"line": 3, "column": 50}}, {"start": {"line": 3, "column": 54}, "end": {"line": 3, "column": 77}}], "line": 3}, "1": {"loc": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": 63}}, "type": "default-arg", "locations": [{"start": {"line": 68, "column": 61}, "end": {"line": 68, "column": 63}}], "line": 68}, "2": {"loc": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 64}}, "type": "if", "locations": [{"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 64}}, {"start": {}, "end": {}}], "line": 71}, "3": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 70}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 70}}, {"start": {}, "end": {}}], "line": 72}, "4": {"loc": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 72}}, "type": "if", "locations": [{"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 72}}, {"start": {}, "end": {}}], "line": 73}, "5": {"loc": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 86}}, "type": "if", "locations": [{"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 86}}, {"start": {}, "end": {}}], "line": 74}, "6": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 86}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 86}}, {"start": {}, "end": {}}], "line": 75}, "7": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 86}}, "type": "if", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 86}}, {"start": {}, "end": {}}], "line": 76}, "8": {"loc": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 86}}, "type": "if", "locations": [{"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": 86}}, {"start": {}, "end": {}}], "line": 77}, "9": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 86}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 86}}, {"start": {}, "end": {}}], "line": 78}, "10": {"loc": {"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 86}}, "type": "if", "locations": [{"start": {"line": 79, "column": 4}, "end": {"line": 79, "column": 86}}, {"start": {}, "end": {}}], "line": 79}, "11": {"loc": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "type": "if", "locations": [{"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, {"start": {}, "end": {}}], "line": 80}, "12": {"loc": {"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 5}}, {"start": {}, "end": {}}], "line": 85}, "13": {"loc": {"start": {"line": 94, "column": 4}, "end": {"line": 103, "column": 10}}, "type": "default-arg", "locations": [{"start": {"line": 103, "column": 8}, "end": {"line": 103, "column": 10}}], "line": 94}, "14": {"loc": {"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 69}}, "type": "if", "locations": [{"start": {"line": 107, "column": 4}, "end": {"line": 107, "column": 69}}, {"start": {}, "end": {}}], "line": 107}, "15": {"loc": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 72}}, "type": "if", "locations": [{"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 72}}, {"start": {}, "end": {}}], "line": 108}, "16": {"loc": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 86}}, "type": "if", "locations": [{"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 86}}, {"start": {}, "end": {}}], "line": 109}, "17": {"loc": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 86}}, "type": "if", "locations": [{"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 86}}, {"start": {}, "end": {}}], "line": 110}, "18": {"loc": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 86}}, "type": "if", "locations": [{"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": 86}}, {"start": {}, "end": {}}], "line": 111}, "19": {"loc": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 86}}, "type": "if", "locations": [{"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 86}}, {"start": {}, "end": {}}], "line": 112}, "20": {"loc": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 101}}, "type": "if", "locations": [{"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 101}}, {"start": {}, "end": {}}], "line": 113}, "21": {"loc": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 101}}, "type": "if", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 101}}, {"start": {}, "end": {}}], "line": 114}, "22": {"loc": {"start": {"line": 117, "column": 4}, "end": {"line": 119, "column": 5}}, "type": "if", "locations": [{"start": {"line": 117, "column": 4}, "end": {"line": 119, "column": 5}}, {"start": {}, "end": {}}], "line": 117}, "23": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": 5}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 128, "column": 5}}, {"start": {}, "end": {}}], "line": 126}, "24": {"loc": {"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, "type": "if", "locations": [{"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, {"start": {}, "end": {}}], "line": 134}, "25": {"loc": {"start": {"line": 142, "column": 4}, "end": {"line": 144, "column": 5}}, "type": "if", "locations": [{"start": {"line": 142, "column": 4}, "end": {"line": 144, "column": 5}}, {"start": {}, "end": {}}], "line": 142}, "26": {"loc": {"start": {"line": 151, "column": 4}, "end": {"line": 153, "column": 5}}, "type": "if", "locations": [{"start": {"line": 151, "column": 4}, "end": {"line": 153, "column": 5}}, {"start": {}, "end": {}}], "line": 151}}, "s": {"0": 4, "1": 4, "2": 576, "3": 576, "4": 4, "5": 576, "6": 1, "7": 576, "8": 574, "9": 576, "10": 0, "11": 576, "12": 0, "13": 576, "14": 0, "15": 576, "16": 0, "17": 576, "18": 0, "19": 576, "20": 0, "21": 576, "22": 0, "23": 0, "24": 576, "25": 572, "26": 131, "27": 441, "28": 2, "29": 2, "30": 0, "31": 2, "32": 0, "33": 2, "34": 0, "35": 2, "36": 0, "37": 2, "38": 0, "39": 2, "40": 0, "41": 2, "42": 0, "43": 2, "44": 0, "45": 2, "46": 2, "47": 1, "48": 1, "49": 15, "50": 13, "51": 2, "52": 11, "53": 15, "54": 13, "55": 1, "56": 12, "57": 15, "58": 13, "59": 1, "60": 12, "61": 1, "62": 1, "63": 0, "64": 1}, "f": {"0": 576, "1": 0, "2": 2, "3": 15, "4": 15, "5": 15, "6": 1}, "b": {"0": [4, 4], "1": [2], "2": [4, 572], "3": [1, 575], "4": [574, 2], "5": [0, 576], "6": [0, 576], "7": [0, 576], "8": [0, 576], "9": [0, 576], "10": [0, 576], "11": [0, 576], "12": [131, 441], "13": [2], "14": [0, 2], "15": [0, 2], "16": [0, 2], "17": [0, 2], "18": [0, 2], "19": [0, 2], "20": [0, 2], "21": [0, 2], "22": [1, 1], "23": [2, 11], "24": [1, 12], "25": [1, 12], "26": [0, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "d2bbbd2e0c00c8529869e68c01bf5fe1ed00d972"}, "/app/src/test-utils/index.tsx": {"path": "/app/src/test-utils/index.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 21}, "end": {"line": 8, "column": 31}}, "1": {"start": {"line": 8, "column": 5}, "end": {"line": 8, "column": 31}}, "2": {"start": {"line": 14, "column": 37}, "end": {"line": 42, "column": 1}}, "3": {"start": {"line": 44, "column": 33}, "end": {"line": 48, "column": 1}}, "4": {"start": {"line": 51, "column": 38}, "end": {"line": 52, "column": 48}}, "5": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 48}}, "6": {"start": {"line": 52, "column": 25}, "end": {"line": 52, "column": 47}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 21}, "end": {"line": 5, "column": 22}}, "loc": {"start": {"line": 8, "column": 5}, "end": {"line": 8, "column": 31}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 51, "column": 38}, "end": {"line": 51, "column": 39}}, "loc": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": 48}}, "line": 52}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 52, "column": 14}, "end": {"line": 52, "column": 15}}, "loc": {"start": {"line": 52, "column": 25}, "end": {"line": 52, "column": 47}}, "line": 52}}, "branchMap": {}, "s": {"0": 4, "1": 49, "2": 4, "3": 4, "4": 4, "5": 0, "6": 0}, "f": {"0": 49, "1": 0, "2": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5f23159baf59ce3d0c47940d6488d77f817fbcc5"}}