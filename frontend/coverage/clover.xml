<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750988213054" clover="3.2.0">
  <project timestamp="1750988213055" name="All files">
    <metrics statements="235" coveredstatements="181" conditionals="141" coveredconditionals="108" methods="61" coveredmethods="41" elements="437" coveredelements="330" complexity="0" loc="235" ncloc="235" packages="9" files="12" classes="12"/>
    <package name="src">
      <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="App.tsx" path="/app/src/App.tsx">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
      </file>
      <file name="index.tsx" path="/app/src/index.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.components.DataTable">
      <metrics statements="28" coveredstatements="16" conditionals="18" coveredconditionals="12" methods="13" coveredmethods="5"/>
      <file name="DataTable.tsx" path="/app/src/components/DataTable/DataTable.tsx">
        <metrics statements="28" coveredstatements="16" conditionals="18" coveredconditionals="12" methods="13" coveredmethods="5"/>
        <line num="10" count="2" type="stmt"/>
        <line num="11" count="44" type="stmt"/>
        <line num="12" count="44" type="stmt"/>
        <line num="13" count="44" type="stmt"/>
        <line num="15" count="44" type="stmt"/>
        <line num="16" count="4" type="stmt"/>
        <line num="17" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="4" type="stmt"/>
        <line num="22" count="4" type="stmt"/>
        <line num="25" count="44" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="44" type="stmt"/>
        <line num="35" count="308" type="cond" truecount="2" falsecount="0"/>
        <line num="36" count="44" type="cond" truecount="1" falsecount="1"/>
        <line num="39" count="44" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="31" type="stmt"/>
        <line num="98" count="4" type="stmt"/>
      </file>
    </package>
    <package name="src.components.FilterPanel">
      <metrics statements="28" coveredstatements="28" conditionals="10" coveredconditionals="8" methods="9" coveredmethods="9"/>
      <file name="FilterPanel.tsx" path="/app/src/components/FilterPanel/FilterPanel.tsx">
        <metrics statements="28" coveredstatements="28" conditionals="10" coveredconditionals="8" methods="9" coveredmethods="9"/>
        <line num="18" count="2" type="stmt"/>
        <line num="24" count="49" type="stmt"/>
        <line num="25" count="49" type="stmt"/>
        <line num="26" count="49" type="stmt"/>
        <line num="27" count="49" type="stmt"/>
        <line num="28" count="49" type="stmt"/>
        <line num="31" count="49" type="stmt"/>
        <line num="32" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="33" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="34" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="49" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="43" count="49" type="stmt"/>
        <line num="44" count="2" type="stmt"/>
        <line num="45" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="49" count="49" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="51" count="2" type="stmt"/>
        <line num="52" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="55" count="49" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="60" count="49" type="stmt"/>
        <line num="102" count="78" type="stmt"/>
        <line num="118" count="78" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.components.Visualization3D">
      <metrics statements="5" coveredstatements="5" conditionals="12" coveredconditionals="12" methods="2" coveredmethods="2"/>
      <file name="Visualization3D.tsx" path="/app/src/components/Visualization3D/Visualization3D.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="12" coveredconditionals="12" methods="2" coveredmethods="2"/>
        <line num="11" count="2" type="stmt"/>
        <line num="12" count="39" type="cond" truecount="2" falsecount="0"/>
        <line num="13" count="3" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="28" count="36" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="36" coveredstatements="36" conditionals="9" coveredconditionals="7" methods="7" coveredmethods="7"/>
      <file name="useFilterOptions.ts" path="/app/src/hooks/useFilterOptions.ts">
        <metrics statements="17" coveredstatements="17" conditionals="2" coveredconditionals="1" methods="3" coveredmethods="3"/>
        <line num="16" count="2" type="stmt"/>
        <line num="17" count="37" type="stmt"/>
        <line num="22" count="37" type="stmt"/>
        <line num="23" count="37" type="stmt"/>
        <line num="25" count="37" type="stmt"/>
        <line num="26" count="14" type="stmt"/>
        <line num="27" count="14" type="stmt"/>
        <line num="28" count="14" type="stmt"/>
        <line num="30" count="14" type="stmt"/>
        <line num="31" count="14" type="stmt"/>
        <line num="37" count="10" type="stmt"/>
        <line num="43" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="44" count="2" type="stmt"/>
        <line num="45" count="2" type="stmt"/>
        <line num="47" count="12" type="stmt"/>
        <line num="51" count="14" type="stmt"/>
        <line num="54" count="37" type="stmt"/>
      </file>
      <file name="useVisualizationData.ts" path="/app/src/hooks/useVisualizationData.ts">
        <metrics statements="19" coveredstatements="19" conditionals="7" coveredconditionals="6" methods="4" coveredmethods="4"/>
        <line num="11" count="2" type="stmt"/>
        <line num="14" count="869" type="stmt"/>
        <line num="15" count="869" type="stmt"/>
        <line num="16" count="869" type="stmt"/>
        <line num="18" count="869" type="cond" truecount="1" falsecount="0"/>
        <line num="19" count="573" type="stmt"/>
        <line num="20" count="573" type="stmt"/>
        <line num="22" count="573" type="stmt"/>
        <line num="23" count="573" type="stmt"/>
        <line num="29" count="439" type="stmt"/>
        <line num="31" count="130" type="cond" truecount="1" falsecount="1"/>
        <line num="32" count="130" type="stmt"/>
        <line num="33" count="130" type="stmt"/>
        <line num="35" count="569" type="stmt"/>
        <line num="39" count="869" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="44" count="869" type="stmt"/>
        <line num="45" count="571" type="stmt"/>
        <line num="48" count="869" type="stmt"/>
      </file>
    </package>
    <package name="src.mocks">
      <metrics statements="24" coveredstatements="23" conditionals="14" coveredconditionals="14" methods="9" coveredmethods="8"/>
      <file name="handlers.ts" path="/app/src/mocks/handlers.ts">
        <metrics statements="23" coveredstatements="22" conditionals="14" coveredconditionals="14" methods="9" coveredmethods="8"/>
        <line num="4" count="7" type="stmt"/>
        <line num="7" count="7" type="stmt"/>
        <line num="61" count="7" type="stmt"/>
        <line num="73" count="7" type="stmt"/>
        <line num="91" count="7" type="stmt"/>
        <line num="94" count="441" type="stmt"/>
        <line num="95" count="441" type="stmt"/>
        <line num="96" count="441" type="stmt"/>
        <line num="99" count="441" type="stmt"/>
        <line num="101" count="441" type="cond" truecount="4" falsecount="0"/>
        <line num="102" count="12" type="stmt"/>
        <line num="105" count="441" type="cond" truecount="4" falsecount="0"/>
        <line num="106" count="2" type="stmt"/>
        <line num="109" count="441" type="cond" truecount="2" falsecount="0"/>
        <line num="110" count="440" type="stmt"/>
        <line num="113" count="441" type="stmt"/>
        <line num="121" count="441" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="131" count="11" type="stmt"/>
        <line num="135" count="12" type="stmt"/>
        <line num="139" count="12" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
      </file>
      <file name="server.ts" path="/app/src/mocks/server.ts">
        <metrics statements="1" coveredstatements="1" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="5" count="7" type="stmt"/>
      </file>
    </package>
    <package name="src.pages">
      <metrics statements="58" coveredstatements="23" conditionals="26" coveredconditionals="19" methods="10" coveredmethods="3"/>
      <file name="MainPage.tsx" path="/app/src/pages/MainPage.tsx">
        <metrics statements="58" coveredstatements="23" conditionals="26" coveredconditionals="19" methods="10" coveredmethods="3"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="29" type="stmt"/>
        <line num="12" count="29" type="stmt"/>
        <line num="13" count="29" type="stmt"/>
        <line num="14" count="29" type="stmt"/>
        <line num="15" count="29" type="stmt"/>
        <line num="17" count="29" type="stmt"/>
        <line num="18" count="29" type="stmt"/>
        <line num="19" count="29" type="stmt"/>
        <line num="20" count="29" type="stmt"/>
        <line num="23" count="29" type="stmt"/>
        <line num="24" count="29" type="stmt"/>
        <line num="27" count="29" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="32" count="29" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="29" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="29" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="29" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="29" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="29" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="29" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="47" coveredstatements="45" conditionals="52" coveredconditionals="36" methods="7" coveredmethods="6"/>
      <file name="api.ts" path="/app/src/services/api.ts">
        <metrics statements="47" coveredstatements="45" conditionals="52" coveredconditionals="36" methods="7" coveredmethods="6"/>
        <line num="3" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="66" count="4" type="stmt"/>
        <line num="69" count="576" type="stmt"/>
        <line num="71" count="576" type="cond" truecount="2" falsecount="0"/>
        <line num="72" count="576" type="cond" truecount="2" falsecount="0"/>
        <line num="73" count="576" type="cond" truecount="2" falsecount="0"/>
        <line num="74" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="75" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="76" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="77" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="78" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="79" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="80" count="576" type="cond" truecount="1" falsecount="1"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="576" type="stmt"/>
        <line num="85" count="572" type="cond" truecount="2" falsecount="0"/>
        <line num="86" count="131" type="stmt"/>
        <line num="88" count="441" type="stmt"/>
        <line num="105" count="2" type="stmt"/>
        <line num="107" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="108" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="109" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="110" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="111" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="112" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="113" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="114" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="116" count="2" type="stmt"/>
        <line num="117" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="118" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="125" count="15" type="stmt"/>
        <line num="126" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="127" count="2" type="stmt"/>
        <line num="129" count="11" type="stmt"/>
        <line num="133" count="15" type="stmt"/>
        <line num="134" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="135" count="1" type="stmt"/>
        <line num="137" count="12" type="stmt"/>
        <line num="141" count="15" type="stmt"/>
        <line num="142" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="143" count="1" type="stmt"/>
        <line num="145" count="12" type="stmt"/>
        <line num="150" count="1" type="stmt"/>
        <line num="151" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.test-utils">
      <metrics statements="6" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="1"/>
      <file name="index.tsx" path="/app/src/test-utils/index.tsx">
        <metrics statements="6" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="1"/>
        <line num="5" count="4" type="stmt"/>
        <line num="8" count="49" type="stmt"/>
        <line num="14" count="4" type="stmt"/>
        <line num="44" count="4" type="stmt"/>
        <line num="51" count="4" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
