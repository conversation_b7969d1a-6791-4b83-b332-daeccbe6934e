# Project Context

This file contains important context, patterns, conventions, and decisions about the codebase that agent based coding
tools can reference and utilize while working on the project.

## Guiding Principles

- Simple is better than complex
- Don't build more than you need

## Overview

Loom is a application that allows users to explore large embedding datasets. The application allows users to upload 
datasets, view them in 3D space using a variety of dimensionality reduction algorithms and filter the points using a
variety of methods.

## Architecture Decisions

- This application consists of a postgres database, a FastAPI server and a React frontend.
- This application supports 4 different modes: dev, test, staging and production.
- This directory contains a make file that should be used for all build, lint and development tasks.

## Basic make file commands

```
make dev-start
make dev-stop

make test
make test-backend
make test-frontend

make lint
make lint-backend
make lint-frontend

make build
make build-backend
make build-frontend
```
